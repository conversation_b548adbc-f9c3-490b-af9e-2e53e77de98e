#!/usr/bin/env python3
"""
Toggle Paper Trading Helper Script
Used by auto_trading_mode.bat to toggle paper trading mode
"""

import sys
import json
import os

def toggle_paper_trading(enabled):
    """Toggle paper trading mode in configuration"""
    config_file = 'config/auto_mode/schedule.json'
    
    try:
        # Read current configuration
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
        else:
            config = {}
        
        # Update paper trading setting
        config['paper_trading'] = enabled
        
        # Save configuration
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Also update main config
        main_config_file = 'config/config.json'
        if os.path.exists(main_config_file):
            with open(main_config_file, 'r') as f:
                main_config = json.load(f)
            
            main_config['trading']['paper_trading'] = enabled
            main_config['advanced']['paper_trading_mode'] = enabled
            
            with open(main_config_file, 'w') as f:
                json.dump(main_config, f, indent=2)
        
        mode = "PAPER TRADING" if enabled else "LIVE TRADING"
        print(f"Successfully switched to {mode} mode")
        
    except Exception as e:
        print(f"Error toggling paper trading: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python toggle_paper_trading.py <true|false>")
        sys.exit(1)
    
    enabled = sys.argv[1].lower() == 'true'
    toggle_paper_trading(enabled) 