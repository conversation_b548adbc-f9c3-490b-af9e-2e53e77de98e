#!/usr/bin/env python3
"""
Toggle Paper Trading Helper Script
Used by auto_trading_mode.bat to toggle paper trading mode
"""

import sys
import json
import os

def toggle_paper_trading(enabled):
    """Toggle paper trading mode in configuration"""
    config_file = 'config/auto_mode/schedule.json'

    try:
        # Ensure directories exist
        os.makedirs('config/auto_mode', exist_ok=True)

        # Default configuration
        default_config = {
            'enabled': False,
            'investment_amount': 30.0,
            'paper_trading': True,
            'paper_balance': 1000.0,
            'start_time': '09:00',
            'end_time': '17:00',
            'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
            'max_runtime_hours': 8,
            'auto_restart': True,
            'restart_interval_hours': 24
        }

        # Read current configuration
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    content = f.read().strip()
                    if content:
                        config = json.loads(content)
                        # Ensure all required fields exist
                        for key, value in default_config.items():
                            if key not in config:
                                config[key] = value
                    else:
                        config = default_config.copy()
            except json.JSONDecodeError:
                config = default_config.copy()
        else:
            config = default_config.copy()

        # Update paper trading setting
        config['paper_trading'] = enabled

        # Save configuration
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

        # Also update main config
        main_config_file = 'config/config.json'
        if os.path.exists(main_config_file):
            try:
                with open(main_config_file, 'r') as f:
                    main_config = json.load(f)

                # Ensure trading section exists
                if 'trading' not in main_config:
                    main_config['trading'] = {}
                if 'advanced' not in main_config:
                    main_config['advanced'] = {}

                main_config['trading']['paper_trading'] = enabled
                main_config['advanced']['paper_trading_mode'] = enabled

                with open(main_config_file, 'w') as f:
                    json.dump(main_config, f, indent=2)
            except Exception as e:
                print(f"Warning: Could not update main config: {e}")

        mode = "PAPER TRADING" if enabled else "LIVE TRADING"
        print(f"Successfully switched to {mode} mode")

    except Exception as e:
        print(f"Error toggling paper trading: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python toggle_paper_trading.py <true|false>")
        sys.exit(1)

    enabled = sys.argv[1].lower() == 'true'
    toggle_paper_trading(enabled)