"""
Paper Trading Module

This module provides paper trading functionality that simulates real trading
without using actual money. It tracks virtual portfolio balance, executes
simulated trades, and provides realistic trading experience for testing strategies.

Features:
- Virtual portfolio management
- Realistic order execution simulation
- Slippage simulation
- Commission simulation
- Performance tracking
- Risk management testing
"""

import os
import json
import time
import logging
import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd

# Configure logging
logger = logging.getLogger(__name__)

class PaperTradingEngine:
    """Paper trading engine that simulates real trading"""
    
    def __init__(self, initial_balance: float = 1000.0, config: Dict = None):
        """
        Initialize paper trading engine
        
        Args:
            initial_balance: Starting balance in USDT
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Portfolio state
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.positions = {}  # {symbol: {quantity, entry_price, entry_time, stop_loss, take_profit}}
        self.trade_history = []
        self.order_history = []
        
        # Trading settings
        self.commission_rate = self.config.get('commission_rate', 0.001)  # 0.1% commission
        self.slippage_percent = self.config.get('slippage_percent', 0.1)  # 0.1% slippage
        self.simulate_slippage = self.config.get('simulate_slippage', True)
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit_loss = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = initial_balance
        
        # Data file paths
        self.portfolio_file = self.config.get('portfolio_file', 'data/paper_portfolio.json')
        self.trades_file = self.config.get('trades_file', 'data/paper_trades.json')
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.portfolio_file), exist_ok=True)
        
        # Load existing portfolio if available
        self._load_portfolio()
        
        logger.info(f"Paper trading engine initialized with {self.balance:.2f} USDT")
    
    def _load_portfolio(self):
        """Load portfolio state from file"""
        try:
            if os.path.exists(self.portfolio_file):
                with open(self.portfolio_file, 'r') as f:
                    data = json.load(f)
                    
                    self.balance = data.get('balance', self.initial_balance)
                    self.positions = data.get('positions', {})
                    self.total_trades = data.get('total_trades', 0)
                    self.winning_trades = data.get('winning_trades', 0)
                    self.losing_trades = data.get('losing_trades', 0)
                    self.total_profit_loss = data.get('total_profit_loss', 0.0)
                    self.max_drawdown = data.get('max_drawdown', 0.0)
                    self.peak_balance = data.get('peak_balance', self.initial_balance)
                    
                logger.info(f"Loaded paper trading portfolio: {self.balance:.2f} USDT, {len(self.positions)} positions")
        except Exception as e:
            logger.error(f"Error loading portfolio: {e}")
    
    def _save_portfolio(self):
        """Save portfolio state to file"""
        try:
            data = {
                'balance': self.balance,
                'positions': self.positions,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'total_profit_loss': self.total_profit_loss,
                'max_drawdown': self.max_drawdown,
                'peak_balance': self.peak_balance,
                'last_update': datetime.datetime.now().isoformat()
            }
            
            with open(self.portfolio_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving portfolio: {e}")
    
    def _save_trade_history(self):
        """Save trade history to file"""
        try:
            with open(self.trades_file, 'w') as f:
                json.dump(self.trade_history, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")
    
    def _apply_slippage(self, price: float, side: str) -> float:
        """
        Apply slippage to order price
        
        Args:
            price: Original price
            side: 'BUY' or 'SELL'
            
        Returns:
            Price with slippage applied
        """
        if not self.simulate_slippage:
            return price
        
        slippage_factor = self.slippage_percent / 100.0
        
        if side == 'BUY':
            # Buying at slightly higher price
            return price * (1 + slippage_factor)
        else:
            # Selling at slightly lower price
            return price * (1 - slippage_factor)
    
    def _calculate_commission(self, quantity: float, price: float) -> float:
        """Calculate trading commission"""
        return quantity * price * self.commission_rate
    
    def place_buy_order(self, symbol: str, quantity: float, price: float, 
                       stop_loss: float = None, take_profit: float = None) -> Dict:
        """
        Place a buy order (open long position)
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            quantity: Quantity to buy
            price: Current market price
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            Order result dictionary
        """
        # Apply slippage
        execution_price = self._apply_slippage(price, 'BUY')
        
        # Calculate total cost including commission
        cost = quantity * execution_price
        commission = self._calculate_commission(quantity, execution_price)
        total_cost = cost + commission
        
        # Check if we have enough balance
        if total_cost > self.balance:
            return {
                'status': 'FAILED',
                'error': 'Insufficient balance',
                'required': total_cost,
                'available': self.balance
            }
        
        # Execute the order
        self.balance -= total_cost
        
        # Add to positions
        self.positions[symbol] = {
            'quantity': quantity,
            'entry_price': execution_price,
            'entry_time': datetime.datetime.now().isoformat(),
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'commission_paid': commission,
            'side': 'LONG'
        }
        
        # Record order
        order = {
            'symbol': symbol,
            'side': 'BUY',
            'quantity': quantity,
            'price': execution_price,
            'commission': commission,
            'timestamp': datetime.datetime.now().isoformat(),
            'status': 'FILLED'
        }
        self.order_history.append(order)
        
        # Save state
        self._save_portfolio()
        
        logger.info(f"[PAPER] BUY {quantity} {symbol} at {execution_price:.6f} (Cost: {total_cost:.2f} USDT)")
        
        return {
            'status': 'FILLED',
            'symbol': symbol,
            'side': 'BUY',
            'quantity': quantity,
            'price': execution_price,
            'commission': commission,
            'total_cost': total_cost
        }
    
    def place_sell_order(self, symbol: str, quantity: float = None, price: float = None) -> Dict:
        """
        Place a sell order (close long position)
        
        Args:
            symbol: Trading symbol
            quantity: Quantity to sell (None = sell all)
            price: Current market price
            
        Returns:
            Order result dictionary
        """
        if symbol not in self.positions:
            return {
                'status': 'FAILED',
                'error': f'No position found for {symbol}'
            }
        
        position = self.positions[symbol]
        sell_quantity = quantity or position['quantity']
        
        if sell_quantity > position['quantity']:
            return {
                'status': 'FAILED',
                'error': 'Insufficient position size'
            }
        
        # Apply slippage
        execution_price = self._apply_slippage(price, 'SELL')
        
        # Calculate proceeds
        proceeds = sell_quantity * execution_price
        commission = self._calculate_commission(sell_quantity, execution_price)
        net_proceeds = proceeds - commission
        
        # Calculate profit/loss
        entry_cost = sell_quantity * position['entry_price']
        profit_loss = net_proceeds - entry_cost - position['commission_paid'] * (sell_quantity / position['quantity'])
        
        # Update balance
        self.balance += net_proceeds
        
        # Record trade
        trade = {
            'symbol': symbol,
            'side': 'LONG',
            'entry_price': position['entry_price'],
            'exit_price': execution_price,
            'quantity': sell_quantity,
            'entry_time': position['entry_time'],
            'exit_time': datetime.datetime.now().isoformat(),
            'profit_loss': profit_loss,
            'commission_total': position['commission_paid'] * (sell_quantity / position['quantity']) + commission,
            'return_percent': (profit_loss / entry_cost) * 100
        }
        self.trade_history.append(trade)
        
        # Update statistics
        self.total_trades += 1
        self.total_profit_loss += profit_loss
        
        if profit_loss > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Update drawdown tracking
        if self.balance > self.peak_balance:
            self.peak_balance = self.balance
        else:
            current_drawdown = (self.peak_balance - self.balance) / self.peak_balance
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
        
        # Update or remove position
        if sell_quantity == position['quantity']:
            del self.positions[symbol]
        else:
            self.positions[symbol]['quantity'] -= sell_quantity
        
        # Record order
        order = {
            'symbol': symbol,
            'side': 'SELL',
            'quantity': sell_quantity,
            'price': execution_price,
            'commission': commission,
            'timestamp': datetime.datetime.now().isoformat(),
            'status': 'FILLED'
        }
        self.order_history.append(order)
        
        # Save state
        self._save_portfolio()
        self._save_trade_history()
        
        logger.info(f"[PAPER] SELL {sell_quantity} {symbol} at {execution_price:.6f} (P/L: {profit_loss:.2f} USDT)")
        
        return {
            'status': 'FILLED',
            'symbol': symbol,
            'side': 'SELL',
            'quantity': sell_quantity,
            'price': execution_price,
            'commission': commission,
            'profit_loss': profit_loss,
            'return_percent': trade['return_percent']
        }
    
    def check_stop_loss_take_profit(self, symbol: str, current_price: float) -> Optional[Dict]:
        """
        Check if stop loss or take profit should be triggered
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            Order result if triggered, None otherwise
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        
        # Check stop loss
        if position.get('stop_loss') and current_price <= position['stop_loss']:
            logger.info(f"[PAPER] Stop loss triggered for {symbol} at {current_price}")
            return self.place_sell_order(symbol, price=current_price)
        
        # Check take profit
        if position.get('take_profit') and current_price >= position['take_profit']:
            logger.info(f"[PAPER] Take profit triggered for {symbol} at {current_price}")
            return self.place_sell_order(symbol, price=current_price)
        
        return None
    
    def get_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """
        Calculate total portfolio value including open positions
        
        Args:
            current_prices: Dictionary of current prices {symbol: price}
            
        Returns:
            Total portfolio value in USDT
        """
        total_value = self.balance
        
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position_value = position['quantity'] * current_prices[symbol]
                total_value += position_value
        
        return total_value
    
    def get_performance_stats(self) -> Dict:
        """Get comprehensive performance statistics"""
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        total_return = ((self.balance - self.initial_balance) / self.initial_balance) * 100
        
        return {
            'initial_balance': self.initial_balance,
            'current_balance': self.balance,
            'total_return': total_return,
            'total_profit_loss': self.total_profit_loss,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown * 100,
            'open_positions': len(self.positions),
            'average_trade': self.total_profit_loss / self.total_trades if self.total_trades > 0 else 0
        }
    
    def reset_portfolio(self, new_balance: float = None):
        """Reset portfolio to initial state"""
        self.balance = new_balance or self.initial_balance
        self.positions = {}
        self.trade_history = []
        self.order_history = []
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit_loss = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = self.balance
        
        self._save_portfolio()
        self._save_trade_history()
        
        logger.info(f"Paper trading portfolio reset to {self.balance:.2f} USDT")

# Global paper trading engine instance
_paper_engine = None

def get_paper_trading_engine(config: Dict = None) -> PaperTradingEngine:
    """Get the global paper trading engine instance"""
    global _paper_engine
    
    if _paper_engine is None:
        initial_balance = config.get('paper_balance', 1000.0) if config else 1000.0
        _paper_engine = PaperTradingEngine(initial_balance, config)
    
    return _paper_engine 