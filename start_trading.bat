@echo off
setlocal enabledelayedexpansion
mode con: cols=100 lines=30
color 0A

echo ========================================================================
echo                  ENHANCED TRADING BOT STARTUP SCRIPT
echo ========================================================================
echo.

:: Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

:: Create necessary directories
echo Creating necessary directories...
if not exist config mkdir config
if not exist data mkdir data
if not exist logs mkdir logs
if not exist web\templates mkdir web\templates
if not exist web\static mkdir web\static

:: Check if pip is installed
python -m pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: pip is not installed. Attempting to install pip...
    python -m ensurepip --default-pip
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install pip.
        echo Please install pip manually and try again.
        pause
        exit /b 1
    )
    echo pip installed successfully.
)

:: Check for required packages
echo Checking for required packages...
set MISSING_PACKAGES=0

:: Check for python-binance
python -c "import binance" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for pandas
python -c "import pandas" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for ta
python -c "import ta" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for python-dotenv
python -c "import dotenv" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for flask
python -c "import flask" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Install missing packages if needed
if %MISSING_PACKAGES% equ 1 (
    echo Some required packages are missing. Installing now...

    if exist requirements.txt (
        python -m pip install -r requirements.txt
    ) else (
        echo requirements.txt not found. Installing packages individually...
        python -m pip install python-binance pandas ta python-dotenv flask
    )

    if %errorlevel% neq 0 (
        echo ERROR: Failed to install required packages.
        echo Please install them manually:
        echo python -m pip install python-binance pandas ta python-dotenv flask
        pause
        exit /b 1
    ) else (
        echo All required packages installed successfully.
    )
)

:: Trading Mode Selection
echo.
echo ========================================================================
echo                       SELECT TRADING MODE
echo ========================================================================
echo.
echo  [1] PAPER TRADING (Virtual money - Safe for testing)
echo  [2] LIVE TRADING (Real money - Requires API keys)
echo.
set /p TRADING_MODE="Select trading mode (1 or 2): "

if "%TRADING_MODE%"=="1" (
    set MODE_NAME=PAPER TRADING
    set PAPER_MODE=true
    echo.
    echo Selected: PAPER TRADING MODE
    echo - Uses virtual money for safe testing
    echo - No real trades will be executed
    echo - Perfect for strategy validation
) else if "%TRADING_MODE%"=="2" (
    set MODE_NAME=LIVE TRADING
    set PAPER_MODE=false
    echo.
    echo Selected: LIVE TRADING MODE
    echo - Uses real money
    echo - Requires Binance API keys
    echo - Real trades will be executed
    
    :: Check if .env file exists with API keys for live trading
    if not exist .env (
        echo.
        echo WARNING: .env file not found. API keys are required for live trading.
        echo.
        set /p CREATE_ENV="Would you like to create a .env file now? (Y/N): "
        if /i "%CREATE_ENV%"=="Y" (
            echo Creating .env file...
            echo.
            set /p API_KEY="Enter your Binance API key: "
            set /p API_SECRET="Enter your Binance API secret: "

            echo BINANCE_API_KEY=!API_KEY!> .env
            echo BINANCE_API_SECRET=!API_SECRET!>> .env

            echo .env file created successfully.
        ) else (
            echo Cannot proceed with live trading without API keys.
            echo Switching to paper trading mode...
            set MODE_NAME=PAPER TRADING
            set PAPER_MODE=true
        )
    )
) else (
    echo Invalid selection. Defaulting to Paper Trading mode.
    set MODE_NAME=PAPER TRADING
    set PAPER_MODE=true
)

:: Update configuration based on selected mode
echo.
echo Configuring %MODE_NAME% mode...
python -c "
from config.settings import get_settings
settings = get_settings()
settings.set('trading', 'paper_trading', %PAPER_MODE%)
settings.set('advanced', 'paper_trading_mode', %PAPER_MODE%)
settings.save_settings()
print('Configuration updated successfully.')
"

:: Ask for investment amount
echo.
if "%PAPER_MODE%"=="true" (
    echo ========================================================================
    echo                    PAPER TRADING CONFIGURATION
    echo ========================================================================
    echo.
    set /p INVESTMENT_AMOUNT="Enter investment amount per trade in USDT (default: 30): "
    if "!INVESTMENT_AMOUNT!"=="" set INVESTMENT_AMOUNT=30
    
    set /p PAPER_BALANCE="Enter starting virtual balance in USDT (default: 1000): "
    if "!PAPER_BALANCE!"=="" set PAPER_BALANCE=1000
    
    :: Update paper trading balance
    python -c "
from config.settings import get_settings
settings = get_settings()
settings.set('trading', 'paper_balance', !PAPER_BALANCE!)
settings.save_settings()
"
    
    echo.
    echo Paper Trading Configuration:
    echo - Virtual Balance: !PAPER_BALANCE! USDT
    echo - Investment per trade: !INVESTMENT_AMOUNT! USDT
) else (
    echo ========================================================================
    echo                     LIVE TRADING CONFIGURATION
    echo ========================================================================
    echo.
    set /p INVESTMENT_AMOUNT="Enter investment amount per trade in USDT (default: 30): "
    if "!INVESTMENT_AMOUNT!"=="" set INVESTMENT_AMOUNT=30
    
    echo.
    echo Live Trading Configuration:
    echo - Investment per trade: !INVESTMENT_AMOUNT! USDT
    echo - Mode: REAL MONEY TRADING
)

:: Ensure required directories exist
echo Creating required directories...
if not exist config mkdir config
if not exist data mkdir data
if not exist logs mkdir logs
if not exist web\templates mkdir web\templates
if not exist web\static mkdir web\static

:: Check if config file exists
if not exist config\config.json (
    echo Creating default configuration file...
    python -c "import json; from config.settings import DEFAULT_SETTINGS; open('config/config.json', 'w').write(json.dumps(DEFAULT_SETTINGS, indent=2))"
)

echo.
echo ========================================================================
echo                       STARTING TRADING BOT
echo ========================================================================
echo.
echo Trading Mode: %MODE_NAME%
echo Investment amount: %INVESTMENT_AMOUNT% USDT
if "%PAPER_MODE%"=="true" (
    echo Virtual Balance: %PAPER_BALANCE% USDT
)
echo Trading period: 7 days
echo.
echo The bot will:
echo  1. Automatically analyze and select high-volatility coins from Binance
echo  2. Execute trades based on technical analysis
echo  3. Manage positions with trailing stop-loss
echo  4. Track performance and generate reports
echo.
echo A web interface will be available at: http://localhost:5000
echo.
echo Press any key to start the bot...
pause > nul

:: Start the trading bot
echo Starting trading bot in %MODE_NAME% mode...
start "Enhanced Trading Bot - %MODE_NAME%" cmd /c "python main.py --amount %INVESTMENT_AMOUNT% & pause"

:: Wait for the bot to initialize
timeout /t 5 > nul

:: Start the web monitor explicitly
echo Starting web monitor...
start "Web Monitor" cmd /c "python -c "from web.monitor import create_templates, start_web_monitor; create_templates(); start_web_monitor('0.0.0.0', 5000)" & pause"

:: Wait for the web monitor to initialize
timeout /t 5 > nul

:: Open the web interface
echo Opening web interface...
start http://localhost:5000

echo.
echo ========================================================================
echo                       BOT STARTED SUCCESSFULLY
echo ========================================================================
echo.
echo The trading bot is now running in %MODE_NAME% mode.
echo The web interface is open in your browser.
echo.
if "%PAPER_MODE%"=="true" (
    echo NOTE: You are in PAPER TRADING mode - no real money is at risk.
    echo All trades are simulated with virtual funds.
) else (
    echo WARNING: You are in LIVE TRADING mode - real money is at risk!
    echo Monitor your trades carefully.
)
echo.
echo To stop the bot, close the trading bot window or run stop_trading.bat
echo.
echo Press any key to exit this window...
pause > nul

endlocal
