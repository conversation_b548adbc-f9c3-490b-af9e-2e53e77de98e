"""
Create .env file for Binance API credentials
"""
import os

def create_env_file():
    """Create .env file with user input for API credentials"""
    
    if os.path.exists('.env'):
        print("✓ .env file already exists")
        return True
    
    print("\n=== Binance API Setup ===")
    print("You need to create API keys from your Binance account:")
    print("1. Go to https://www.binance.com/en/my/settings/api-management")
    print("2. Create a new API key")
    print("3. Enable 'Enable Trading' permission")
    print("4. DO NOT enable 'Enable Withdrawals' for security")
    print("5. Copy your API Key and Secret Key")
    print()
    
    # Get API credentials from user
    api_key = input("Enter your Binance API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return False
    
    api_secret = input("Enter your Binance API Secret: ").strip()
    if not api_secret:
        print("❌ API Secret is required!")
        return False
    
    # Ask about trading mode
    print("\n=== Trading Mode ===")
    print("1. Paper Trading (Recommended for testing)")
    print("2. Live Trading (Real money)")
    
    while True:
        mode = input("Choose trading mode (1 or 2): ").strip()
        if mode in ['1', '2']:
            break
        print("Please enter 1 or 2")
    
    paper_trading = mode == '1'
    
    # Create .env file
    env_content = f"""# Binance API Configuration
BINANCE_API_KEY={api_key}
BINANCE_API_SECRET={api_secret}

# Trading Mode (true for paper trading, false for live trading)
PAPER_TRADING={'true' if paper_trading else 'false'}

# Optional: Binance Testnet (for testing)
# BINANCE_TESTNET=true
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print(f"\n✓ .env file created successfully!")
        print(f"✓ Trading mode: {'Paper Trading' if paper_trading else 'Live Trading'}")
        print("\n⚠️  IMPORTANT SECURITY NOTES:")
        print("- Never share your .env file with anyone")
        print("- The .env file contains sensitive API credentials")
        print("- Make sure .env is in your .gitignore file")
        
        if not paper_trading:
            print("\n⚠️  LIVE TRADING WARNING:")
            print("- You are using LIVE TRADING mode with real money")
            print("- Start with small amounts to test the bot")
            print("- Monitor the bot closely during initial runs")
            print("- The bot will execute real trades on Binance")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

if __name__ == "__main__":
    create_env_file()
