@echo off
echo Testing batch configuration loading...
echo.

:: Test read_config.py
echo Running read_config.py...
python read_config.py

:: Check if temp file exists
if exist config\auto_mode\temp.txt (
    echo ✓ temp.txt file created successfully
    echo.
    echo Loading configuration variables...

    :: Load values from temp file
    for /f "usebackq delims== tokens=1,2" %%a in ("config\auto_mode\temp.txt") do (
        set "%%a=%%b"
    )

    echo ✓ Configuration loaded:
    echo   ENABLED=%ENABLED%
    echo   AMOUNT=%AMOUNT%
    echo   PAPER_TRADING=%PAPER_TRADING%
    echo   PAPER_BALANCE=%PAPER_BALANCE%
    echo   START_TIME=%START_TIME%
    echo   END_TIME=%END_TIME%
    echo   DAYS=%DAYS%
    echo   MAX_RUNTIME=%MAX_RUNTIME%
    echo   AUTO_RESTART=%AUTO_RESTART%
    echo   RESTART_INTERVAL=%RESTART_INTERVAL%
    echo.

    :: Test paper trading toggle
    echo Testing paper trading toggle...
    if "%PAPER_TRADING%" == "true" (
        echo Current mode: PAPER TRADING
        echo Testing switch to LIVE TRADING...
        python toggle_paper_trading.py false
        echo Testing switch back to PAPER TRADING...
        python toggle_paper_trading.py true
    ) else (
        echo Current mode: LIVE TRADING
        echo Testing switch to PAPER TRADING...
        python toggle_paper_trading.py true
    )

    echo.
    echo ✅ ALL TESTS PASSED!
    echo The auto_trading_mode.bat should now work properly.

) else (
    echo ❌ temp.txt file not found!
    echo There may be an issue with read_config.py
)

echo.
echo Press any key to exit...
pause > nul
