import os, sys, time, json, datetime, subprocess, signal
from datetime import datetime, timedelta

def load_config():
    """Load configuration from file with error handling"""
    default_config = {
        'enabled': False,
        'investment_amount': 30.0,
        'start_time': '09:00',
        'end_time': '17:00',
        'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        'max_runtime_hours': 8,
        'auto_restart': True,
        'restart_interval_hours': 24
    }
    try:
        if os.path.exists('config/auto_mode/schedule.json'):
            with open('config/auto_mode/schedule.json', 'r') as f:
                content = f.read().strip()
                if content:
                    config = json.loads(content)
                    # Ensure all required fields exist
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
        # If we get here, either the file doesn't exist or is empty/invalid
        print("Using default configuration")
        # Create the config directory if it doesn't exist
        os.makedirs('config/auto_mode', exist_ok=True)
        # Write the default config to file
        with open('config/auto_mode/schedule.json', 'w') as f:
            json.dump(default_config, f, indent=2)
        return default_config
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return default_config

def is_trading_day(config):
    today = datetime.now().strftime('%A')
    return today in config['days']

def is_trading_time(config):
    now = datetime.now().time()
    start_time = datetime.strptime(config['start_time'], '%H:%M').time()
    end_time = datetime.strptime(config['end_time'], '%H:%M').time()

    # Handle overnight trading (e.g., 22:00 to 06:00)
    if start_time <= end_time:
        return start_time <= now <= end_time
    else:
        return now >= start_time or now <= end_time

def start_bot(config):
    print(f"Starting trading bot with {config['investment_amount']} USDT...")
    try:
        # Ensure config directory exists
        os.makedirs('config', exist_ok=True)
        os.makedirs('config/auto_mode', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('logs', exist_ok=True)

        # Kill any existing bot processes
        os.system('taskkill /FI "WINDOWTITLE eq Trading Bot*" /F ')
        os.system('taskkill /FI "WINDOWTITLE eq Web Monitor*" /F ')
        time.sleep(2)  # Wait for processes to terminate

        # Start the trading bot
        bot_process = subprocess.Popen(['python', 'main.py', '--amount', str(config['investment_amount'])])

        # Start the web monitor
        monitor_process = subprocess.Popen([
            'python', '-c',
            'from web.monitor import create_templates, start_web_monitor; create_templates(); start_web_monitor("0.0.0.0", 5000)'
        ])

        print("Trading bot and web monitor started successfully.")
        return bot_process, monitor_process
    except Exception as e:
        print(f"Error starting bot: {e}")
        return None, None

def stop_bot():
    print("Stopping trading bot...")
    os.system('taskkill /FI "WINDOWTITLE eq Trading Bot*" /F ')
    os.system('taskkill /FI "WINDOWTITLE eq Web Monitor*" /F ')
    os.system('taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Trading Bot*" /F ')
    os.system('taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Web Monitor*" /F ')
    print("Trading bot stopped.")

def main():
    print("Starting Auto Mode Runner...")
    bot_process = None
    monitor_process = None
    last_restart = datetime.now()
    start_time = datetime.now()
    error_count = 0  # Track consecutive errors
    max_errors = 5   # Maximum consecutive errors before exiting

    try:
        while True:
            try:
                config = load_config()
                # Reset error count on successful config load

                if not config['enabled']:
                    print("Auto mode is disabled. Exiting...")
                    stop_bot()
                    break
                now = datetime.now()
                is_trading_day_now = is_trading_day(config)
                is_trading_time_now = is_trading_time(config)
                # Check if we need to restart the bot
                if config['auto_restart'] and bot_process is not None:
                    restart_interval = timedelta(hours=config['restart_interval_hours'])
                    if now - last_restart >= restart_interval:
                        print(f"Restart interval reached ({config['restart_interval_hours']} hours). Restarting bot...")
                        stop_bot()
                        time.sleep(5)  # Wait for processes to terminate
                        bot_process, monitor_process = start_bot(config)
                        last_restart = now
                        start_time = now

                # Check if maximum runtime has been reached
                max_runtime = timedelta(hours=config['max_runtime_hours'])
                if bot_process is not None and now - start_time >= max_runtime:
                    print(f"Maximum runtime reached ({config['max_runtime_hours']} hours). Stopping bot...")
                    stop_bot()
                    bot_process = None
                    monitor_process = None

                # Start bot if it's trading time and not already running
                if is_trading_day_now and is_trading_time_now and bot_process is None:
                    print("It's trading time. Starting bot...")
                    bot_process, monitor_process = start_bot(config)
                    last_restart = now
                    start_time = now

                # Stop bot if it's not trading time and it's running
                elif (not is_trading_day_now or not is_trading_time_now) and bot_process is not None:
                    print("Trading time is over. Stopping bot...")
                    stop_bot()
                    bot_process = None
                    monitor_process = None
                # Status update
                if bot_process is not None:
                    runtime = now - start_time
                    hours, remainder = divmod(runtime.total_seconds(), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    print(f"Bot running for {int(hours)}h {int(minutes)}m {int(seconds)}s")
                else:
                    if is_trading_day_now:
                        start_time_str = config['start_time']
                        end_time_str = config['end_time']
                        now_str = now.strftime('%H:%M')
                        print(f"Bot not running. Current time: {now_str}, Trading hours: {start_time_str} - {end_time_str}")
                    else:
                        print(f"Bot not running. Today ({now.strftime('%A')}) is not a trading day.")

                # Sleep before next check
                time.sleep(60)  # Check every minute

            except Exception as e:
                error_count += 1
                print(f"Error in auto mode loop: {e}")
                if error_count >= max_errors:
                    print(f"Too many consecutive errors ({max_errors}). Exiting...")
                    stop_bot()
                    break
                # Sleep before retry
                time.sleep(60)

    except KeyboardInterrupt:
        print("Auto mode runner stopped by user.")
        stop_bot()
    except Exception as e:
        print(f"Error in auto mode runner: {e}")
        stop_bot()

if __name__ == "__main__":
    main()
