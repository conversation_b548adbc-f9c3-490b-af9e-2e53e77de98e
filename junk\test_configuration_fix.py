#!/usr/bin/env python3
"""
Test script to verify all configuration fixes work properly
"""

import os
import sys
import json
import subprocess
import time

def test_read_config():
    """Test read_config.py functionality"""
    print("Testing read_config.py...")
    try:
        result = subprocess.run(['python', 'read_config.py'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ read_config.py executed successfully")
            
            # Check if temp file was created
            if os.path.exists('config/auto_mode/temp.txt'):
                print("✓ temp.txt file created")
                with open('config/auto_mode/temp.txt', 'r') as f:
                    content = f.read()
                    print(f"✓ temp.txt content: {len(content.split())} lines")
                return True
            else:
                print("❌ temp.txt file not created")
                return False
        else:
            print(f"❌ read_config.py failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ read_config.py test failed: {e}")
        return False

def test_toggle_paper_trading():
    """Test toggle_paper_trading.py functionality"""
    print("\nTesting toggle_paper_trading.py...")
    try:
        # Test switching to paper trading
        result = subprocess.run(['python', 'toggle_paper_trading.py', 'true'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ toggle_paper_trading.py (true) executed successfully")
            print(f"✓ Output: {result.stdout.strip()}")
            
            # Test switching to live trading
            result = subprocess.run(['python', 'toggle_paper_trading.py', 'false'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✓ toggle_paper_trading.py (false) executed successfully")
                print(f"✓ Output: {result.stdout.strip()}")
                
                # Switch back to paper trading for safety
                subprocess.run(['python', 'toggle_paper_trading.py', 'true'], 
                             capture_output=True, text=True, timeout=10)
                return True
            else:
                print(f"❌ toggle_paper_trading.py (false) failed: {result.stderr}")
                return False
        else:
            print(f"❌ toggle_paper_trading.py (true) failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ toggle_paper_trading.py test failed: {e}")
        return False

def test_config_files():
    """Test configuration file integrity"""
    print("\nTesting configuration files...")
    try:
        # Test auto mode config
        auto_config_file = 'config/auto_mode/schedule.json'
        if os.path.exists(auto_config_file):
            with open(auto_config_file, 'r') as f:
                auto_config = json.load(f)
            print("✓ Auto mode config loaded successfully")
            print(f"✓ Paper trading: {auto_config.get('paper_trading', 'Not set')}")
        else:
            print("❌ Auto mode config file not found")
            return False
        
        # Test main config
        main_config_file = 'config/config.json'
        if os.path.exists(main_config_file):
            with open(main_config_file, 'r') as f:
                main_config = json.load(f)
            print("✓ Main config loaded successfully")
            print(f"✓ Trading paper_trading: {main_config.get('trading', {}).get('paper_trading', 'Not set')}")
            print(f"✓ Advanced paper_trading_mode: {main_config.get('advanced', {}).get('paper_trading_mode', 'Not set')}")
        else:
            print("❌ Main config file not found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Config file test failed: {e}")
        return False

def test_batch_script_simulation():
    """Simulate batch script operations"""
    print("\nTesting batch script simulation...")
    try:
        # Run read_config.py
        subprocess.run(['python', 'read_config.py'], 
                      capture_output=True, text=True, timeout=10)
        
        # Check if temp file exists and can be read
        if os.path.exists('config/auto_mode/temp.txt'):
            with open('config/auto_mode/temp.txt', 'r') as f:
                lines = f.readlines()
            
            config_vars = {}
            for line in lines:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    config_vars[key] = value
            
            print("✓ Batch script variables loaded:")
            for key, value in config_vars.items():
                print(f"  {key}={value}")
            
            # Check required variables
            required_vars = ['ENABLED', 'AMOUNT', 'PAPER_TRADING', 'PAPER_BALANCE', 
                           'START_TIME', 'END_TIME', 'DAYS', 'MAX_RUNTIME', 
                           'AUTO_RESTART', 'RESTART_INTERVAL']
            
            missing_vars = [var for var in required_vars if var not in config_vars]
            if missing_vars:
                print(f"❌ Missing variables: {missing_vars}")
                return False
            else:
                print("✓ All required variables present")
                return True
        else:
            print("❌ temp.txt file not found")
            return False
    except Exception as e:
        print(f"❌ Batch script simulation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("CONFIGURATION FIX VERIFICATION TEST")
    print("=" * 60)
    
    tests = [
        ("Read Config Test", test_read_config),
        ("Toggle Paper Trading Test", test_toggle_paper_trading),
        ("Config Files Test", test_config_files),
        ("Batch Script Simulation Test", test_batch_script_simulation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name}...")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 ALL CONFIGURATION FIXES VERIFIED!")
        print("\n✅ SYSTEM STATUS: CONFIGURATION ISSUES RESOLVED")
        print("\nThe auto_trading_mode.bat should now work properly:")
        print("1. Option 2 (Switch Trading Mode) will work correctly")
        print("2. All configuration options will persist properly")
        print("3. No more temp file errors")
        print("4. Paper trading toggle will work seamlessly")
    else:
        print("⚠️  Some configuration issues remain. Please check the failures above.")
    
    return passed == total

if __name__ == "__main__":
    main()
