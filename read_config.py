import json
import os

def write_temp_file(config):
    """Write config values to temp file for batch script"""
    try:
        os.makedirs('config/auto_mode', exist_ok=True)
        with open('config/auto_mode/temp.txt', 'w') as f:
            f.write(f"ENABLED={str(config['enabled']).lower()}\n")
            f.write(f"AMOUNT={config['investment_amount']}\n")
            f.write(f"PAPER_TRADING={str(config.get('paper_trading', True)).lower()}\n")
            f.write(f"PAPER_BALANCE={config.get('paper_balance', 1000.0)}\n")
            f.write(f"START_TIME={config['start_time']}\n")
            f.write(f"END_TIME={config['end_time']}\n")
            f.write(f"DAYS={','.join(config['days'])}\n")
            f.write(f"MAX_RUNTIME={config['max_runtime_hours']}\n")
            f.write(f"AUTO_RESTART={str(config['auto_restart']).lower()}\n")
            f.write(f"RESTART_INTERVAL={config['restart_interval_hours']}\n")
    except Exception as e:
        print(f"Error writing temp file: {e}")

try:
    # Ensure config directory exists
    os.makedirs('config/auto_mode', exist_ok=True)

    # Default config
    default_config = {
        'enabled': False,
        'investment_amount': 30.0,
        'paper_trading': True,
        'paper_balance': 1000.0,
        'start_time': '09:00',
        'end_time': '17:00',
        'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        'max_runtime_hours': 8,
        'auto_restart': True,
        'restart_interval_hours': 24
    }

    # Load existing config if it exists
    config = default_config.copy()
    config_file = 'config/auto_mode/schedule.json'

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                content = f.read().strip()
                if content:
                    loaded_config = json.loads(content)
                    # Ensure all required fields exist and update config
                    for key, value in default_config.items():
                        if key not in loaded_config:
                            loaded_config[key] = value
                    config = loaded_config
                else:
                    # File exists but is empty, use defaults
                    config = default_config.copy()
        except (json.JSONDecodeError, Exception) as e:
            print(f"Error loading config, using defaults: {e}")
            config = default_config.copy()
    else:
        # Create the default config file if it doesn't exist
        try:
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            config = default_config.copy()
        except Exception as e:
            print(f"Error creating config file: {e}")
            config = default_config.copy()

    # Write the config values to a temp file for the batch script to read
    write_temp_file(config)

except Exception as e:
    print(f"Critical error: {e}")

    # Create default values in case of error
    default_config = {
        'enabled': False,
        'investment_amount': 30.0,
        'paper_trading': True,
        'paper_balance': 1000.0,
        'start_time': '09:00',
        'end_time': '17:00',
        'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        'max_runtime_hours': 8,
        'auto_restart': True,
        'restart_interval_hours': 24
    }
    write_temp_file(default_config)
