import json
import os

try:
    # Ensure config directory exists
    os.makedirs('config/auto_mode', exist_ok=True)
    
    # Default config
    default_config = {
        'enabled': False,
        'investment_amount': 30.0,
        'paper_trading': True,
        'paper_balance': 1000.0,
        'start_time': '09:00',
        'end_time': '17:00',
        'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        'max_runtime_hours': 8,
        'auto_restart': True,
        'restart_interval_hours': 24
    }
    
    # Load existing config if it exists
    config = default_config.copy()
    if os.path.exists('config/auto_mode/schedule.json'):
        try:
            with open('config/auto_mode/schedule.json', 'r') as f:
                content = f.read().strip()
                if content:
                    loaded_config = json.loads(content)
                    # Update config with loaded values
                    for key, value in loaded_config.items():
                        config[key] = value
        except Exception as e:
            print(f"Error loading config: {e}")
    else:
        # Create the default config file if it doesn't exist
        with open('config/auto_mode/schedule.json', 'w') as f:
            json.dump(default_config, f, indent=2)
    
    # Write the config values to a temp file for the batch script to read
    with open('config/auto_mode/temp.txt', 'w') as f:
        f.write(f"ENABLED={str(config['enabled']).lower()}\n")
        f.write(f"AMOUNT={config['investment_amount']}\n")
        f.write(f"PAPER_TRADING={str(config.get('paper_trading', True)).lower()}\n")
        f.write(f"PAPER_BALANCE={config.get('paper_balance', 1000.0)}\n")
        f.write(f"START_TIME={config['start_time']}\n")
        f.write(f"END_TIME={config['end_time']}\n")
        f.write(f"DAYS={','.join(config['days'])}\n")
        f.write(f"MAX_RUNTIME={config['max_runtime_hours']}\n")
        f.write(f"AUTO_RESTART={str(config['auto_restart']).lower()}\n")
        f.write(f"RESTART_INTERVAL={config['restart_interval_hours']}\n")
    
except Exception as e:
    print(f"Error: {e}")
    
    # Create default values in case of error
    with open('config/auto_mode/temp.txt', 'w') as f:
        f.write("ENABLED=false\n")
        f.write("AMOUNT=30.0\n")
        f.write("PAPER_TRADING=true\n")
        f.write("PAPER_BALANCE=1000.0\n")
        f.write("START_TIME=09:00\n")
        f.write("END_TIME=17:00\n")
        f.write("DAYS=Monday,Tuesday,Wednesday,Thursday,Friday\n")
        f.write("MAX_RUNTIME=8\n")
        f.write("AUTO_RESTART=true\n")
        f.write("RESTART_INTERVAL=24\n")
