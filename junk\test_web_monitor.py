#!/usr/bin/env python3
"""
Test script for the web monitor
"""

import os
import sys
import time
import threading

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_web_monitor():
    """Test the web monitor functionality"""
    try:
        print("Testing web monitor...")
        
        # Import web monitor components
        from web.monitor import create_templates, start_web_monitor, update_trade_data
        
        # Create templates
        print("Creating templates...")
        create_templates()
        print("✓ Templates created successfully")
        
        # Test data update
        print("Testing data update...")
        update_trade_data()
        print("✓ Data update successful")
        
        # Start web monitor in a separate thread
        print("Starting web monitor...")
        web_thread = threading.Thread(
            target=start_web_monitor,
            args=('0.0.0.0', 5000),
            daemon=True
        )
        web_thread.start()
        
        print("✓ Web monitor started successfully")
        print("✓ Web interface should be available at: http://localhost:5000")
        
        # Wait a bit for the server to start
        time.sleep(3)
        
        print("\nWeb monitor is running!")
        print("Press Ctrl+C to stop...")
        
        # Keep the main thread alive
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping web monitor...")
            
        return True
        
    except Exception as e:
        print(f"❌ Web monitor test failed: {e}")
        return False

if __name__ == "__main__":
    test_web_monitor()
