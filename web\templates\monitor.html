
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .card { margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .card-header { font-weight: bold; background-color: #343a40; color: white; }
        .profit { color: green; }
        .loss { color: red; }
        .refresh-time { font-size: 0.8rem; color: #6c757d; text-align: right; }
        .trade-row:hover { background-color: #f1f1f1; }
        .nav-tabs .nav-link { color: #495057; }
        .nav-tabs .nav-link.active { font-weight: bold; }
    </style>
    <meta http-equiv="refresh" content="30">
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4 text-center">Trading Bot Monitor</h1>
        <div class="refresh-time mb-3">Last updated: <span id="last-update"></span></div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Performance Summary</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <p><strong>Total Trades:</strong> <span id="total-trades">0</span></p>
                                <p><strong>Win Rate:</strong> <span id="win-rate">0%</span></p>
                                <p><strong>Profitable Trades:</strong> <span id="profitable-trades">0</span></p>
                                <p><strong>Losing Trades:</strong> <span id="losing-trades">0</span></p>
                            </div>
                            <div class="col-6">
                                <p><strong>Total P/L:</strong> <span id="total-pnl">0.00 USDT</span></p>
                                <p><strong>Total Profit:</strong> <span id="total-profit" class="profit">0.00 USDT</span></p>
                                <p><strong>Total Loss:</strong> <span id="total-loss" class="loss">0.00 USDT</span></p>
                                <p><strong>Daily ROI:</strong> <span id="daily-roi">0.00%</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Active Trades</div>
                    <div class="card-body">
                        <div id="active-trades-container">
                            <p class="text-center text-muted">No active trades</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">Daily Performance</div>
                    <div class="card-body">
                        <div id="daily-stats-container">
                            <p class="text-center text-muted">No daily statistics available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="trades-tab" data-bs-toggle="tab" data-bs-target="#trades" type="button" role="tab" aria-controls="trades" aria-selected="true">Completed Trades</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="coins-tab" data-bs-toggle="tab" data-bs-target="#coins" type="button" role="tab" aria-controls="coins" aria-selected="false">Selected Coins</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">Recent Logs</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="trades" role="tabpanel" aria-labelledby="trades-tab">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Buy Price</th>
                                                <th>Sell Price</th>
                                                <th>Quantity</th>
                                                <th>P/L</th>
                                                <th>P/L %</th>
                                                <th>Date</th>
                                                <th>Duration</th>
                                            </tr>
                                        </thead>
                                        <tbody id="completed-trades">
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">No completed trades</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="coins" role="tabpanel" aria-labelledby="coins-tab">
                                <div id="selected-coins-container">
                                    <p class="text-center text-muted">No coin data available</p>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                                <div id="logs-container" style="max-height: 400px; overflow-y: auto;">
                                    <p class="text-center text-muted">No logs available</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to fetch and update data
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Update performance metrics
                    document.getElementById('total-trades').textContent = data.performance.total_trades;
                    document.getElementById('profitable-trades').textContent = data.performance.profitable_trades;
                    document.getElementById('losing-trades').textContent = data.performance.losing_trades;
                    document.getElementById('win-rate').textContent = data.performance.win_rate.toFixed(1) + '%';
                    
                    // Update total P/L
                    const pnlElement = document.getElementById('total-pnl');
                    pnlElement.textContent = data.performance.total_profit_loss.toFixed(2) + ' USDT';
                    if (data.performance.total_profit_loss > 0) {
                        pnlElement.className = 'profit';
                    } else if (data.performance.total_profit_loss < 0) {
                        pnlElement.className = 'loss';
                    }
                    
                    // Update total profit and loss
                    document.getElementById('total-profit').textContent = data.performance.total_profit.toFixed(2) + ' USDT';
                    document.getElementById('total-loss').textContent = data.performance.total_loss.toFixed(2) + ' USDT';
                    
                    // Update daily ROI
                    const roiElement = document.getElementById('daily-roi');
                    roiElement.textContent = data.performance.daily_roi.toFixed(2) + '%';
                    if (data.performance.daily_roi > 0) {
                        roiElement.className = 'profit';
                    } else if (data.performance.daily_roi < 0) {
                        roiElement.className = 'loss';
                    }
                    
                    // Update active trades
                    const activeTradesContainer = document.getElementById('active-trades-container');
                    if (data.active_trades.length > 0) {
                        let activeTradesHtml = '<div class="table-responsive"><table class="table table-sm">';
                        activeTradesHtml += '<thead><tr><th>Symbol</th><th>Current Price</th><th>P/L</th><th>Time</th></tr></thead><tbody>';
                        
                        data.active_trades.forEach(trade => {
                            const isProfitable = trade.pnl.includes('+') || !trade.pnl.includes('-');
                            activeTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${trade.current_price}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.pnl}</td>
                                <td>${trade.time}</td>
                            </tr>`;
                        });
                        
                        activeTradesHtml += '</tbody></table></div>';
                        activeTradesContainer.innerHTML = activeTradesHtml;
                    } else {
                        activeTradesContainer.innerHTML = '<p class="text-center text-muted">No active trades</p>';
                    }
                    
                    // Update daily statistics
                    const dailyStatsContainer = document.getElementById('daily-stats-container');
                    if (Object.keys(data.daily_trades).length > 0) {
                        let dailyStatsHtml = '<div class="table-responsive"><table class="table table-striped">';
                        dailyStatsHtml += '<thead><tr><th>Date</th><th>Trades</th><th>Profit</th><th>Loss</th><th>Net P/L</th></tr></thead><tbody>';
                        
                        // Get dates and sort them (newest first)
                        const dates = Object.keys(data.daily_trades).sort().reverse();
                        
                        dates.forEach(date => {
                            const trades = data.daily_trades[date];
                            const profit = data.daily_profits[date] || 0;
                            const loss = data.daily_losses[date] || 0;
                            const net = data.daily_net[date] || 0;
                            
                            const isProfitable = net > 0;
                            
                            dailyStatsHtml += `<tr>
                                <td>${date}</td>
                                <td>${trades}</td>
                                <td class="profit">${profit.toFixed(2)} USDT</td>
                                <td class="loss">${loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${net.toFixed(2)} USDT</td>
                            </tr>`;
                        });
                        
                        dailyStatsHtml += '</tbody></table></div>';
                        dailyStatsContainer.innerHTML = dailyStatsHtml;
                    } else {
                        dailyStatsContainer.innerHTML = '<p class="text-center text-muted">No daily statistics available</p>';
                    }
                    
                    // Update completed trades
                    const completedTradesTable = document.getElementById('completed-trades');
                    if (data.completed_trades.length > 0) {
                        let completedTradesHtml = '';
                        
                        data.completed_trades.forEach(trade => {
                            const isProfitable = trade.profit_loss > 0;
                            completedTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${parseFloat(trade.buy_price).toFixed(8)}</td>
                                <td>${parseFloat(trade.sell_price).toFixed(8)}</td>
                                <td>${trade.quantity}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss_pct.toFixed(2)}%</td>
                                <td>${trade.date}</td>
                                <td>${trade.duration_minutes.toFixed(1)} min</td>
                            </tr>`;
                        });
                        
                        completedTradesTable.innerHTML = completedTradesHtml;
                    } else {
                        completedTradesTable.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No completed trades</td></tr>';
                    }
                    
                    // Update selected coins
                    const selectedCoinsContainer = document.getElementById('selected-coins-container');
                    if (data.selected_coins && data.selected_coins.length > 0) {
                        let coinsHtml = '<div class="alert alert-info">Currently monitoring the following coins:</div>';
                        coinsHtml += '<div class="row">';
                        
                        data.selected_coins.forEach(coin => {
                            coinsHtml += `<div class="col-md-3 mb-2"><div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">${coin}</h5>
                                </div>
                            </div></div>`;
                        });
                        
                        coinsHtml += '</div>';
                        selectedCoinsContainer.innerHTML = coinsHtml;
                    } else {
                        selectedCoinsContainer.innerHTML = '<p class="text-center text-muted">No coins selected</p>';
                    }
                    
                    // Update last update time
                    document.getElementById('last-update').textContent = data.last_update;
                })
                .catch(error => console.error('Error fetching data:', error));
                
            // Fetch logs
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    if (data.logs) {
                        const logsContainer = document.getElementById('logs-container');
                        let logsHtml = '<pre style="max-height: 400px; overflow-y: auto;">';
                        
                        data.logs.forEach(line => {
                            logsHtml += line;
                        });
                        
                        logsHtml += '</pre>';
                        logsContainer.innerHTML = logsHtml;
                    }
                })
                .catch(error => console.error('Error fetching logs:', error));
        }
        
        // Initial update
        updateData();
        
        // Update every 10 seconds
        setInterval(updateData, 10000);
    </script>
</body>
</html>
