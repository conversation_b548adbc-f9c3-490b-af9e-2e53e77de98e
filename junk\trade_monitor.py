import os
import json
import time
import threading
import datetime
from flask import Flask, render_template, jsonify
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Global variables to store trading data
trade_data = {
    'active_trades': [],
    'completed_trades': [],
    'performance': {
        'total_trades': 0,
        'profitable_trades': 0,
        'losing_trades': 0,        # Track losing trades
        'total_profit_loss': 0.0,
        'total_profit': 0.0,       # Track total profit separately
        'total_loss': 0.0,         # Track total loss separately
        'win_rate': 0.0,
        'daily_roi': 0.0           # Daily Return on Investment
    },
    'pair_performance': {},
    'daily_profits': {},           # Track profits per day
    'daily_losses': {},            # Track losses per day
    'daily_trades': {},            # Track number of trades per day
    'daily_net': {},               # Track net profit/loss per day
    'last_update': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
}

# Path to trading log file
LOG_FILE = 'trading_log.txt'
TRADE_HISTORY_FILE = 'trade_history.json'

# Function to parse trading log and update data
def update_trade_data():
    global trade_data

    # Check if log file exists
    if not os.path.exists(LOG_FILE):
        logger.warning(f"Log file {LOG_FILE} not found")
        return

    # Check if trade history file exists and load it
    if os.path.exists(TRADE_HISTORY_FILE):
        try:
            with open(TRADE_HISTORY_FILE, 'r') as f:
                trade_history = json.load(f)

                # Convert string dates back to datetime objects for sorting
                for trade in trade_history:
                    if 'date' in trade:
                        trade['date'] = datetime.datetime.strptime(trade['date'], '%Y-%m-%d %H:%M:%S')

                # Sort by date (newest first)
                completed_trades = sorted(trade_history, key=lambda x: x['date'], reverse=True)

                # Calculate daily statistics and overall profit/loss
                daily_profits = {}
                daily_losses = {}
                daily_trades = {}
                daily_net = {}
                total_profit = 0.0
                total_loss = 0.0
                losing_trades = 0

                for trade in trade_history:
                    # Get the date part only
                    trade_date = trade['date'].date().strftime('%Y-%m-%d')

                    # Count trades per day
                    if trade_date not in daily_trades:
                        daily_trades[trade_date] = 0
                        daily_profits[trade_date] = 0.0
                        daily_losses[trade_date] = 0.0
                        daily_net[trade_date] = 0.0

                    daily_trades[trade_date] += 1

                    # Track profit/loss
                    profit_loss = trade['profit_loss']
                    daily_net[trade_date] += profit_loss

                    if profit_loss > 0:
                        daily_profits[trade_date] += profit_loss
                        total_profit += profit_loss
                    else:
                        daily_losses[trade_date] += profit_loss
                        total_loss += profit_loss
                        losing_trades += 1

                # Update the trade data
                trade_data['daily_profits'] = daily_profits
                trade_data['daily_losses'] = daily_losses
                trade_data['daily_trades'] = daily_trades
                trade_data['daily_net'] = daily_net
                trade_data['performance']['total_profit'] = total_profit
                trade_data['performance']['total_loss'] = total_loss
                trade_data['performance']['losing_trades'] = losing_trades

                # Calculate daily ROI based on average daily net profit
                if daily_net:
                    avg_daily_net = sum(daily_net.values()) / len(daily_net)
                    # Assuming TRADE_AMOUNT is 30 USDT
                    trade_data['performance']['daily_roi'] = (avg_daily_net / 30.0) * 100

                # Convert datetime objects back to strings for JSON serialization
                for trade in completed_trades:
                    if 'date' in trade:
                        trade['date'] = trade['date'].strftime('%Y-%m-%d %H:%M:%S')

                trade_data['completed_trades'] = completed_trades
        except Exception as e:
            logger.error(f"Error loading trade history: {e}")

    # Parse log file for active trades and performance metrics
    active_trades = []
    try:
        with open(LOG_FILE, 'r') as f:
            lines = f.readlines()

            # Find active trades
            for i in range(len(lines) - 1, 0, -1):
                line = lines[i]

                # Check for monitoring messages (active trades)
                if "Monitoring" in line and "Current:" in line:
                    parts = line.split('|')
                    if len(parts) >= 3:
                        # Extract symbol from the line
                        symbol_part = parts[0].split("Monitoring")[1].strip()
                        current_part = parts[1].strip()
                        pnl_part = parts[2].strip()

                        # Check if this symbol is already in active_trades
                        if not any(t['symbol'] == symbol_part for t in active_trades):
                            active_trades.append({
                                'symbol': symbol_part,
                                'current_price': current_part.replace('Current:', '').strip(),
                                'pnl': pnl_part.replace('P/L:', '').strip(),
                                'time': line.split(' - ')[0].strip()
                            })

                # Check for performance metrics
                if "Total trades:" in line:
                    trade_data['performance']['total_trades'] = int(line.split("Total trades:")[1].strip())

                if "Win rate:" in line:
                    win_rate_str = line.split("Win rate:")[1].split("%")[0].strip()
                    trade_data['performance']['win_rate'] = float(win_rate_str)

                if "Total P/L:" in line and "USDT" in line:
                    pnl_str = line.split("Total P/L:")[1].split("USDT")[0].strip()
                    trade_data['performance']['total_profit_loss'] = float(pnl_str)

                if "Profitable trades:" in line:
                    profitable_str = line.split("Profitable trades:")[1].split("(")[0].strip()
                    trade_data['performance']['profitable_trades'] = int(profitable_str)

                # Check for pair performance
                if "Best performing pair:" in line:
                    pair_info = line.split("Best performing pair:")[1].strip()
                    pair_name = pair_info.split("with")[0].strip()
                    pair_profit = float(pair_info.split("with")[1].split("USDT")[0].strip())
                    trade_data['pair_performance'][pair_name] = pair_profit

    except Exception as e:
        logger.error(f"Error parsing log file: {e}")

    trade_data['active_trades'] = active_trades
    trade_data['last_update'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# Background thread to update trade data periodically
def background_updater():
    while True:
        try:
            update_trade_data()
            time.sleep(10)  # Update every 10 seconds
        except Exception as e:
            logger.error(f"Error in background updater: {e}")
            time.sleep(30)  # Wait longer if there's an error

# Routes
@app.route('/')
def index():
    return render_template('monitor.html')

@app.route('/api/data')
def get_data():
    return jsonify(trade_data)

# Create HTML template directory and file
def create_template():
    os.makedirs('templates', exist_ok=True)

    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .card { margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .card-header { font-weight: bold; background-color: #343a40; color: white; }
        .profit { color: green; }
        .loss { color: red; }
        .refresh-time { font-size: 0.8rem; color: #6c757d; text-align: right; }
        .trade-row:hover { background-color: #f1f1f1; }
    </style>
    <meta http-equiv="refresh" content="30">
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center">Trading Bot Monitor</h1>
        <div class="refresh-time mb-3">Last updated: <span id="last-update"></span></div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Performance Summary</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <p><strong>Total Trades:</strong> <span id="total-trades">0</span></p>
                                <p><strong>Win Rate:</strong> <span id="win-rate">0%</span></p>
                                <p><strong>Profitable Trades:</strong> <span id="profitable-trades">0</span></p>
                                <p><strong>Losing Trades:</strong> <span id="losing-trades">0</span></p>
                            </div>
                            <div class="col-6">
                                <p><strong>Total P/L:</strong> <span id="total-pnl">0.00 USDT</span></p>
                                <p><strong>Total Profit:</strong> <span id="total-profit" class="profit">0.00 USDT</span></p>
                                <p><strong>Total Loss:</strong> <span id="total-loss" class="loss">0.00 USDT</span></p>
                                <p><strong>Daily ROI:</strong> <span id="daily-roi">0.00%</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Active Trades</div>
                    <div class="card-body">
                        <div id="active-trades-container">
                            <p class="text-center text-muted">No active trades</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">Daily Performance</div>
                    <div class="card-body">
                        <div id="daily-stats-container">
                            <p class="text-center text-muted">No daily statistics available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">Completed Trades</div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Buy Price</th>
                                <th>Sell Price</th>
                                <th>Quantity</th>
                                <th>P/L</th>
                                <th>P/L %</th>
                                <th>Date</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody id="completed-trades">
                            <tr>
                                <td colspan="8" class="text-center text-muted">No completed trades</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to fetch and update data
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Update performance metrics
                    document.getElementById('total-trades').textContent = data.performance.total_trades;
                    document.getElementById('profitable-trades').textContent = data.performance.profitable_trades;
                    document.getElementById('losing-trades').textContent = data.performance.losing_trades;
                    document.getElementById('win-rate').textContent = data.performance.win_rate.toFixed(1) + '%';

                    // Update total P/L
                    const pnlElement = document.getElementById('total-pnl');
                    pnlElement.textContent = data.performance.total_profit_loss.toFixed(2) + ' USDT';
                    if (data.performance.total_profit_loss > 0) {
                        pnlElement.className = 'profit';
                    } else if (data.performance.total_profit_loss < 0) {
                        pnlElement.className = 'loss';
                    }

                    // Update total profit and loss
                    document.getElementById('total-profit').textContent = data.performance.total_profit.toFixed(2) + ' USDT';
                    document.getElementById('total-loss').textContent = data.performance.total_loss.toFixed(2) + ' USDT';

                    // Update daily ROI
                    const roiElement = document.getElementById('daily-roi');
                    roiElement.textContent = data.performance.daily_roi.toFixed(2) + '%';
                    if (data.performance.daily_roi > 0) {
                        roiElement.className = 'profit';
                    } else if (data.performance.daily_roi < 0) {
                        roiElement.className = 'loss';
                    }

                    // Update daily statistics
                    const dailyStatsContainer = document.getElementById('daily-stats-container');
                    if (Object.keys(data.daily_trades).length > 0) {
                        let dailyStatsHtml = '<div class="table-responsive"><table class="table table-striped">';
                        dailyStatsHtml += '<thead><tr><th>Date</th><th>Trades</th><th>Profit</th><th>Loss</th><th>Net P/L</th></tr></thead><tbody>';

                        // Get dates and sort them (newest first)
                        const dates = Object.keys(data.daily_trades).sort().reverse();

                        dates.forEach(date => {
                            const trades = data.daily_trades[date];
                            const profit = data.daily_profits[date] || 0;
                            const loss = data.daily_losses[date] || 0;
                            const net = data.daily_net[date] || 0;

                            const isProfitable = net > 0;

                            dailyStatsHtml += `<tr>
                                <td>${date}</td>
                                <td>${trades}</td>
                                <td class="profit">${profit.toFixed(2)} USDT</td>
                                <td class="loss">${loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${net.toFixed(2)} USDT</td>
                            </tr>`;
                        });

                        dailyStatsHtml += '</tbody></table></div>';
                        dailyStatsContainer.innerHTML = dailyStatsHtml;
                    } else {
                        dailyStatsContainer.innerHTML = '<p class="text-center text-muted">No daily statistics available</p>';
                    }

                    // Update active trades
                    const activeTradesContainer = document.getElementById('active-trades-container');
                    if (data.active_trades.length > 0) {
                        let activeTradesHtml = '<div class="table-responsive"><table class="table table-sm">';
                        activeTradesHtml += '<thead><tr><th>Symbol</th><th>Current Price</th><th>P/L</th><th>Time</th></tr></thead><tbody>';

                        data.active_trades.forEach(trade => {
                            const isProfitable = trade.pnl.includes('+') || !trade.pnl.includes('-');
                            activeTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${trade.current_price}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.pnl}</td>
                                <td>${trade.time}</td>
                            </tr>`;
                        });

                        activeTradesHtml += '</tbody></table></div>';
                        activeTradesContainer.innerHTML = activeTradesHtml;
                    } else {
                        activeTradesContainer.innerHTML = '<p class="text-center text-muted">No active trades</p>';
                    }

                    // Update completed trades
                    const completedTradesTable = document.getElementById('completed-trades');
                    if (data.completed_trades.length > 0) {
                        let completedTradesHtml = '';

                        data.completed_trades.forEach(trade => {
                            const isProfitable = trade.profit_loss > 0;
                            completedTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${parseFloat(trade.buy_price).toFixed(8)}</td>
                                <td>${parseFloat(trade.sell_price).toFixed(8)}</td>
                                <td>${trade.quantity}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss_pct.toFixed(2)}%</td>
                                <td>${trade.date}</td>
                                <td>${trade.duration_minutes.toFixed(1)} min</td>
                            </tr>`;
                        });

                        completedTradesTable.innerHTML = completedTradesHtml;
                    } else {
                        completedTradesTable.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No completed trades</td></tr>';
                    }

                    // Update last update time
                    document.getElementById('last-update').textContent = data.last_update;
                })
                .catch(error => console.error('Error fetching data:', error));
        }

        // Initial update
        updateData();

        // Update every 10 seconds
        setInterval(updateData, 10000);
    </script>
</body>
</html>
"""

    with open('templates/monitor.html', 'w') as f:
        f.write(html_content)

if __name__ == '__main__':
    # Create template directory and file
    create_template()

    # Start background updater thread
    updater_thread = threading.Thread(target=background_updater, daemon=True)
    updater_thread.start()

    # Start Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)
