"""
Settings Module

This module manages the configuration settings for the trading bot.
It loads settings from a config file and provides default values.
"""

import os
import json
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Default settings
DEFAULT_SETTINGS = {
    # Trading parameters
    "trading": {
        "stop_loss_percent": 5.0,
        "take_profit_percent": 15.0,
        "trailing_stop_percent": 3.0,
        "max_trades_per_day": 10,
        "investment_period_days": 7,
        "api_cooldown": 0.5,
        "default_investment": 30.0,
        "max_trade_duration_hours": 8,
        "paper_trading": True,
        "paper_balance": 1000.0
    },
    
    # Coin selection parameters
    "coin_selection": {
        "max_coins": 10,
        "min_volume_usdt": 10000000,
        "min_price_change": 3.0,
        "max_price_change": 50.0,
        "blacklist": ["BUSD", "USDC", "TUSD", "DAI", "USDP", "USDT"],
        "update_interval": 3600,
        "timeframes": ["1h", "4h", "1d"],
        "volatility_weight": 0.3,
        "volume_weight": 0.2,
        "trend_weight": 0.3,
        "momentum_weight": 0.2
    },
    
    # Signal generation parameters
    "signal": {
        "min_score": 7,
        "use_multi_timeframe": True,
        "timeframes": ["5m", "15m"]
    },
    
    # Web monitor settings
    "web_monitor": {
        "host": "0.0.0.0",
        "port": 5000,
        "refresh_interval": 30,
        "enable_notifications": False
    },
    
    # Logging settings
    "logging": {
        "level": "INFO",
        "file": "logs/trading_log.txt",
        "max_file_size": 10485760,  # 10 MB
        "backup_count": 5
    },
    
    # File paths
    "paths": {
        "trade_history": "data/trade_history.json",
        "selected_coins": "data/selected_coins.json",
        "performance_data": "data/performance_data.json",
        "backup_dir": "data/backups"
    },
    
    # Notification settings
    "notifications": {
        "enabled": False,
        "email": {
            "enabled": False,
            "smtp_server": "",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from_email": "",
            "to_email": ""
        },
        "telegram": {
            "enabled": False,
            "bot_token": "",
            "chat_id": ""
        }
    },
    
    # Advanced settings
    "advanced": {
        "enable_dynamic_stop_loss": True,
        "enable_auto_update": False,
        "debug_mode": False,
        "save_market_data": True,
        "backtest_mode": False,
        "paper_trading_mode": True,
        "simulate_slippage": True,
        "slippage_percent": 0.1
    }
}

class Settings:
    """Class for managing bot settings"""
    
    def __init__(self, config_file="config/config.json"):
        """
        Initialize settings
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.settings = DEFAULT_SETTINGS.copy()
        
        # Ensure config directory exists
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        # Load settings from file if it exists
        self.load_settings()
    
    def load_settings(self):
        """Load settings from the configuration file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_settings = json.load(f)
                    self._merge_settings(self.settings, loaded_settings)
                logger.info(f"Loaded settings from {self.config_file}")
            else:
                logger.info(f"Config file {self.config_file} not found, using defaults")
                self.save_settings()  # Create the default config file
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
    
    def save_settings(self):
        """Save current settings to the configuration file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
            logger.info(f"Saved settings to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
    
    def get(self, section, key=None, default=None):
        """
        Get a setting value
        
        Args:
            section: The settings section (e.g., 'trading')
            key: The specific setting key (optional)
            default: Default value if the setting doesn't exist
            
        Returns:
            The setting value or the entire section if key is None
        """
        if section not in self.settings:
            return default
        
        if key is None:
            return self.settings[section]
        
        return self.settings[section].get(key, default)
    
    def set(self, section, key, value):
        """
        Set a setting value
        
        Args:
            section: The settings section (e.g., 'trading')
            key: The specific setting key
            value: The value to set
        """
        if section not in self.settings:
            self.settings[section] = {}
        
        self.settings[section][key] = value
    
    def update_section(self, section, values):
        """
        Update an entire section of settings
        
        Args:
            section: The settings section (e.g., 'trading')
            values: Dictionary of values to update
        """
        if section not in self.settings:
            self.settings[section] = {}
        
        self.settings[section].update(values)
    
    def _merge_settings(self, target, source):
        """
        Recursively merge source into target
        
        Args:
            target: Target dictionary
            source: Source dictionary
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_settings(target[key], value)
            else:
                target[key] = value

# Create a global settings instance
settings = Settings()

def get_settings():
    """Get the global settings instance"""
    return settings
