"""
Trading Bot Main Module

This is the main entry point for the enhanced trading bot.
It integrates all components and provides a unified interface.

Features:
- Dynamic coin selection based on Binance trends
- Advanced technical analysis for trade signals
- Comprehensive risk management
- Performance tracking and reporting
- Web-based monitoring interface
- Automatic backups and notifications
"""

import os
import sys
import time
import json
import math
import logging
import datetime
import threading
import argparse
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
from ta.trend import EMAIndicator, MACD, SMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.volatility import BollingerBands

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import modules
from config.settings import get_settings
from modules.coin_analyzer import get_selected_coins
from modules.paper_trading import get_paper_trading_engine
from utils.backup import get_backup_manager
from utils.notifications import get_notification_manager

# Configure logging
def setup_logging():
    """Set up logging configuration"""
    settings = get_settings()
    log_config = settings.get('logging')

    log_level = getattr(logging, log_config.get('level', 'INFO'))
    log_file = log_config.get('file', 'logs/trading_log.txt')

    # Ensure log directory exists
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

class TradingBot:
    """Main Trading Bot class that integrates all components"""

    def __init__(self, investment_amount=None):
        """
        Initialize the trading bot

        Args:
            investment_amount: Amount to invest per trade (in USDT)
        """
        logger.info("Initializing Trading Bot...")

        # Load settings
        self.settings = get_settings()

        # Load API keys from .env
        load_dotenv()
        self.api_key = os.getenv("BINANCE_API_KEY")
        self.api_secret = os.getenv("BINANCE_API_SECRET")

        if not self.api_key or not self.api_secret:
            logger.error("API keys not found. Please set BINANCE_API_KEY and BINANCE_API_SECRET in .env file")
            sys.exit(1)

        # Initialize Binance client
        self.client = Client(self.api_key, self.api_secret)

        # Check if paper trading mode is enabled
        self.paper_trading_enabled = (
            self.settings.get('trading', {}).get('paper_trading', False) or
            self.settings.get('advanced', {}).get('paper_trading_mode', False)
        )

        # Initialize paper trading engine if enabled
        if self.paper_trading_enabled:
            paper_config = {
                'paper_balance': self.settings.get('trading', {}).get('paper_balance', 1000.0),
                'commission_rate': 0.001,  # 0.1% commission
                'slippage_percent': self.settings.get('advanced', {}).get('slippage_percent', 0.1),
                'simulate_slippage': self.settings.get('advanced', {}).get('simulate_slippage', True),
                'portfolio_file': 'data/paper_portfolio.json',
                'trades_file': 'data/paper_trades.json'
            }
            self.paper_engine = get_paper_trading_engine(paper_config)
            logger.info(f"Paper trading mode enabled with {paper_config['paper_balance']:.2f} USDT virtual balance")
        else:
            self.paper_engine = None
            logger.info("Live trading mode enabled")

        # Set investment amount
        trading_settings = self.settings.get('trading')
        self.investment_amount = investment_amount or trading_settings.get('default_investment')
        logger.info(f"Using investment amount: {self.investment_amount} USDT")

        # Initialize components
        self._init_components()

        # Trading parameters
        self.stop_loss_percent = trading_settings.get('stop_loss_percent')
        self.take_profit_percent = trading_settings.get('take_profit_percent')
        self.trailing_stop_percent = trading_settings.get('trailing_stop_percent')
        self.max_trades_per_day = trading_settings.get('max_trades_per_day')
        self.investment_period_days = trading_settings.get('investment_period_days')
        self.api_cooldown = trading_settings.get('api_cooldown')

        # Trading state
        self.start_date = datetime.datetime.now()
        self.end_date = self.start_date + datetime.timedelta(days=self.investment_period_days)
        self.total_trades = 0
        self.profitable_trades = 0
        self.total_profit_loss = 0.0
        self.trade_history = []
        self.pair_performance = {}
        self.daily_profits = {}
        self.active_trades = {}

        # Load trading history if available
        self._load_trade_history()

        # Initialize trading pairs
        self.pairs = []
        self._update_trading_pairs()

        logger.info(f"Trading Bot initialized with {len(self.pairs)} pairs")
        logger.info(f"Trading period: {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")
        logger.info(f"Target profit: {self.take_profit_percent}% per trade")

    def _init_components(self):
        """Initialize bot components"""
        # Initialize backup manager
        backup_config = {
            'backup_dir': self.settings.get('paths', {}).get('backup_dir', 'data/backups'),
            'max_backups': 10,
            'backup_interval': 86400,  # 24 hours
            'files_to_backup': [
                self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json'),
                self.settings.get('paths', {}).get('selected_coins', 'data/selected_coins.json'),
                self.settings.get('paths', {}).get('performance_data', 'data/performance_data.json'),
                'config/config.json',
                self.settings.get('logging', {}).get('file', 'logs/trading_log.txt')
            ]
        }
        self.backup_manager = get_backup_manager(backup_config)

        # Initialize notification manager
        self.notification_manager = get_notification_manager(self.settings.get('notifications'))

    def _load_trade_history(self):
        """Load trade history from file"""
        history_file = self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json')

        try:
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    self.trade_history = json.load(f)

                # Calculate statistics from history
                self.total_trades = len(self.trade_history)
                self.profitable_trades = sum(1 for trade in self.trade_history if trade.get('profit_loss', 0) > 0)
                self.total_profit_loss = sum(trade.get('profit_loss', 0) for trade in self.trade_history)

                # Calculate pair performance
                for trade in self.trade_history:
                    symbol = trade.get('symbol')
                    if symbol not in self.pair_performance:
                        self.pair_performance[symbol] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}

                    self.pair_performance[symbol]['trades'] += 1
                    if trade.get('profit_loss', 0) > 0:
                        self.pair_performance[symbol]['wins'] += 1
                    self.pair_performance[symbol]['profit_loss'] += trade.get('profit_loss', 0)

                # Calculate daily profits
                for trade in self.trade_history:
                    if isinstance(trade.get('date'), str):
                        date = datetime.datetime.strptime(trade.get('date'), '%Y-%m-%d %H:%M:%S').date()
                    else:
                        date = trade.get('date').date()

                    date_str = date.strftime('%Y-%m-%d')
                    if date_str not in self.daily_profits:
                        self.daily_profits[date_str] = 0.0

                    self.daily_profits[date_str] += trade.get('profit_loss', 0)

                logger.info(f"Loaded {self.total_trades} trades from history")
                logger.info(f"Total P/L: {self.total_profit_loss:.2f} USDT")

        except Exception as e:
            logger.error(f"Error loading trade history: {e}")

    def _save_trade_history(self):
        """Save trade history to file"""
        history_file = self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json')

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(history_file), exist_ok=True)

            # Convert datetime objects to strings
            serializable_history = []
            for trade in self.trade_history:
                trade_copy = trade.copy()
                if isinstance(trade_copy.get('date'), datetime.datetime):
                    trade_copy['date'] = trade_copy['date'].strftime('%Y-%m-%d %H:%M:%S')
                serializable_history.append(trade_copy)

            with open(history_file, 'w') as f:
                json.dump(serializable_history, f, indent=2)

            logger.debug(f"Saved {len(serializable_history)} trades to history file")

        except Exception as e:
            logger.error(f"Error saving trade history: {e}")

    def _update_trading_pairs(self):
        """Update the list of trading pairs based on market analysis"""
        try:
            # Get coin selection configuration
            coin_config = self.settings.get('coin_selection')

            # Get selected coins from analyzer
            self.pairs = get_selected_coins(self.client, coin_config)

            if not self.pairs:
                logger.warning("No coins selected by analyzer, using default pairs")
                self.pairs = [
                    "INJUSDT",   # Injective - DeFi with high volatility
                    "FETUSDT",   # Fetch.ai - AI token with growth potential
                    "AGIXUSDT",  # SingularityNET - AI token with volatility
                    "APTUSDT",   # Aptos - Layer 1 with strong momentum
                    "SUIUSDT",   # Sui - New L1 blockchain with volatility
                    "PEPEUSDT",  # Pepe - Meme coin with high volatility
                    "FLOKIUSDT"  # Floki - Meme coin with high volatility
                ]

            logger.info(f"Updated trading pairs: {', '.join(self.pairs)}")

        except Exception as e:
            logger.error(f"Error updating trading pairs: {e}")
            # Use default pairs if there's an error
            self.pairs = [
                "INJUSDT",   # Injective - DeFi with high volatility
                "FETUSDT",   # Fetch.ai - AI token with growth potential
                "AGIXUSDT",  # SingularityNET - AI token with volatility
                "APTUSDT",   # Aptos - Layer 1 with strong momentum
                "SUIUSDT",   # Sui - New L1 blockchain with volatility
                "PEPEUSDT",  # Pepe - Meme coin with high volatility
                "FLOKIUSDT"  # Floki - Meme coin with high volatility
            ]

    def start(self):
        """Start the trading bot"""
        logger.info("Starting trading bot main loop...")

        try:
            # Create initial backup
            self.backup_manager.create_backup()

            # Start web monitor in a separate thread if enabled
            if self.settings.get('web_monitor', {}).get('enabled', True):
                self._start_web_monitor()

            # Main trading loop
            while datetime.datetime.now() < self.end_date:
                # Check account balance
                self._check_account_balance()

                # Update trading pairs periodically
                if datetime.datetime.now().minute % 30 == 0:  # Every 30 minutes
                    self._update_trading_pairs()

                # Scan all pairs for trading opportunities
                for pair in self.pairs:
                    # Update pair performance tracking
                    if pair not in self.pair_performance:
                        self.pair_performance[pair] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}

                    # Execute trade function
                    self._analyze_and_trade(pair)

                    # Sleep between pairs to avoid API rate limits
                    time.sleep(self.api_cooldown)

                # Generate daily report at midnight
                if datetime.datetime.now().hour == 0 and datetime.datetime.now().minute < 5:
                    self._generate_performance_report()
                    # Create daily backup
                    self.backup_manager.create_backup()

                logger.info("[CYCLE] Scan completed. Waiting before next scan...")
                time.sleep(300)  # Wait 5 minutes between full scans

            # Generate final report at the end of the investment period
            logger.info("Investment period completed!")
            self._generate_performance_report()

        except KeyboardInterrupt:
            logger.info("Bot stopped by user.")
            self._generate_performance_report()
        except Exception as e:
            logger.error(f"Critical error: {e}")
            self._generate_performance_report()

    def _check_account_balance(self):
        """Check if there's enough balance for trading"""
        try:
            if self.paper_trading_enabled:
                # For paper trading, check virtual balance
                if self.paper_engine.balance < self.investment_amount:
                    logger.warning(f"Insufficient paper trading balance: {self.paper_engine.balance:.2f}. Required: {self.investment_amount}")
                    return False
                return True
            else:
                # For live trading, check actual Binance balance
                account = self.client.get_account()
                usdt_balance = float(next((asset['free'] for asset in account['balances'] if asset['asset'] == 'USDT'), 0))

                if usdt_balance < self.investment_amount:
                    logger.warning(f"Insufficient USDT balance: {usdt_balance}. Required: {self.investment_amount}")
                    logger.info("Waiting for 30 minutes before checking again...")
                    time.sleep(1800)  # Wait 30 minutes
                    return False

                return True

        except Exception as e:
            logger.error(f"Error checking account balance: {e}")
            return False

    def _get_klines(self, symbol, interval=Client.KLINE_INTERVAL_5MINUTE, limit=100):
        """Get historical klines data with improved error handling"""
        try:
            data = self.client.get_klines(symbol=symbol, interval=interval, limit=limit)
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'num_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'])

            # Convert columns to numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])

            # Add timestamp in readable format
            df['date'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except BinanceAPIException as e:
            logger.error(f"[{symbol}] API error: {e}")
            return None
        except Exception as e:
            logger.error(f"[{symbol}] Unexpected error: {e}")
            return None

    def _calculate_indicators(self, df):
        """Calculate multiple technical indicators for better signal accuracy"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']

        # Trend indicators
        df['ema20'] = EMAIndicator(close, window=20).ema_indicator()
        df['ema50'] = EMAIndicator(close, window=50).ema_indicator()
        df['sma200'] = SMAIndicator(close, window=50).sma_indicator()

        # Momentum indicators
        df['rsi'] = RSIIndicator(close, window=14).rsi()
        stoch = StochasticOscillator(high, low, close, window=14, smooth_window=3)
        df['stoch_k'] = stoch.stoch()
        df['stoch_d'] = stoch.stoch_signal()

        # MACD
        macd = MACD(close)
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        df['macd_diff'] = macd.macd_diff()

        # Volatility
        bb = BollingerBands(close, window=20, window_dev=2)
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        df['bb_mid'] = bb.bollinger_mavg()
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_mid']

        # Volume analysis
        df['volume_ema'] = EMAIndicator(volume, window=20).ema_indicator()
        df['volume_ratio'] = volume / df['volume_ema']

        return df

    def _generate_signal(self, df):
        """Enhanced signal generation with multiple confirmation factors"""
        # Calculate all indicators
        df = self._calculate_indicators(df)

        # Get latest values
        latest = df.iloc[-1]
        prev = df.iloc[-2]

        # Signal strength score (0-10)
        score = 0

        # Trend conditions
        if latest['close'] > latest['ema20']:
            score += 1
        if latest['ema20'] > latest['ema50']:
            score += 1
        if latest['close'] > prev['close']:
            score += 1

        # Momentum conditions
        if 30 <= latest['rsi'] <= 40:  # Oversold but starting to recover
            score += 2
        if latest['rsi'] > prev['rsi']:  # Rising RSI
            score += 1
        if latest['stoch_k'] < 30 and latest['stoch_k'] > latest['stoch_d']:  # Stochastic crossover from oversold
            score += 1

        # MACD conditions
        if latest['macd'] > latest['macd_signal'] and prev['macd'] <= prev['macd_signal']:  # Fresh MACD crossover
            score += 2
        if latest['macd_diff'] > 0 and latest['macd_diff'] > prev['macd_diff']:  # Increasing MACD histogram
            score += 1

        # Volatility conditions
        if latest['close'] < latest['bb_lower']:  # Price below lower Bollinger Band (potential bounce)
            score += 1
        if latest['bb_width'] > df['bb_width'].rolling(20).mean().iloc[-1]:  # Expanding volatility
            score += 1

        # Volume conditions
        if latest['volume_ratio'] > 1.5:  # Volume spike
            score += 1

        # Check if we have a strong enough signal (threshold of 7 out of 12 possible points)
        logger.info(f"Signal strength for {df.index.name or 'Unknown'}: {score}/12")
        return score >= 7

    def _analyze_and_trade(self, symbol):
        """
        Analyze a symbol and execute a trade if conditions are met

        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
        """
        # Check if we've reached the end of our investment period
        if datetime.datetime.now() > self.end_date:
            logger.info(f"Investment period ended.")
            return

        # Check if we've exceeded our daily trade limit
        today = datetime.datetime.now().date()
        today_trades = sum(1 for t in self.trade_history if
                          isinstance(t.get('date'), str) and
                          datetime.datetime.strptime(t['date'], '%Y-%m-%d %H:%M:%S').date() == today)
        if today_trades >= self.max_trades_per_day:
            logger.info(f"Daily trade limit reached ({self.max_trades_per_day}). Waiting for next day.")
            return

        logger.info(f"[SCAN] Analyzing {symbol}...")

        # Get data from multiple timeframes for confirmation
        df_5m = self._get_klines(symbol, interval=Client.KLINE_INTERVAL_5MINUTE)
        df_15m = self._get_klines(symbol, interval=Client.KLINE_INTERVAL_15MINUTE)

        if df_5m is None or df_15m is None:
            logger.error(f"Could not retrieve data for {symbol}")
            return

        # Check for signals on both timeframes
        signal_5m = self._generate_signal(df_5m)
        signal_15m = self._generate_signal(df_15m)

        # Only proceed if we have signals on both timeframes
        if not (signal_5m and signal_15m):
            logger.info(f"[INFO] No strong entry signal for {symbol}")
            return

        # Execute the trade
        self._execute_trade(symbol)

    def _execute_trade(self, symbol):
        """Execute a trade for the given symbol"""
        try:
            # Get current price and calculate quantity
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])

            if self.paper_trading_enabled:
                # Execute paper trade
                result = self.paper_engine.buy_market(
                    symbol=symbol,
                    quantity=self.investment_amount / price,
                    price=price,
                    stop_loss=price * (1 - self.stop_loss_percent / 100),
                    take_profit=price * (1 + self.take_profit_percent / 100)
                )

                if result['status'] == 'FILLED':
                    logger.info(f"[PAPER BUY] {symbol} @ {price:.8f} | Qty: {result['quantity']:.8f}")
                    self._monitor_paper_position(symbol)
                else:
                    logger.error(f"[PAPER TRADE FAILED] {symbol}: {result.get('error', 'Unknown error')}")
            else:
                # Execute live trade
                self._execute_live_trade(symbol, price)

        except Exception as e:
            logger.error(f"[ERROR] Trade execution error for {symbol}: {e}")

    def _execute_live_trade(self, symbol, price):
        """Execute a live trade on Binance"""
        try:
            # Get symbol info for precision
            symbol_info = next((s for s in self.client.get_exchange_info()['symbols'] if s['symbol'] == symbol), None)
            if not symbol_info:
                logger.error(f"Could not get symbol info for {symbol}")
                return

            # Get the quantity precision
            lot_size_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE'), None)
            if not lot_size_filter:
                logger.error(f"Could not get LOT_SIZE filter for {symbol}")
                return

            # Calculate step size decimal places
            step_size = float(lot_size_filter['stepSize'])
            precision = int(round(-math.log10(step_size)))

            # Calculate quantity with correct precision
            quantity = round(self.investment_amount / price, precision)

            # Check minimum notional value
            min_notional_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'MIN_NOTIONAL'), None)
            if min_notional_filter and price * quantity < float(min_notional_filter['minNotional']):
                logger.warning(f"Order value too small for {symbol}. Minimum: {min_notional_filter['minNotional']} USDT")
                return

            logger.info(f"[SIGNAL] Strong signal detected: {symbol} @ {price} | Qty: {quantity}")

            # Execute buy order
            order = self.client.order_market_buy(symbol=symbol, quantity=quantity)
            buy_price = float(order['fills'][0]['price'])
            buy_time = datetime.datetime.now()

            logger.info(f"[BUY] Order executed: {buy_price} | Time: {buy_time}")

            # Monitor the position
            self._monitor_live_position(symbol, quantity, buy_price, buy_time)

        except BinanceAPIException as e:
            logger.error(f"[ERROR] Binance API error: {e}")
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error: {e}")

    def _monitor_paper_position(self, symbol):
        """Monitor a paper trading position"""
        # Paper trading positions are automatically managed by the paper engine
        # We just need to check for completed trades periodically
        position = self.paper_engine.positions.get(symbol)
        if position:
            logger.info(f"[PAPER MONITOR] {symbol} position opened at {position['entry_price']:.8f}")

    def _monitor_live_position(self, symbol, quantity, buy_price, buy_time):
        """Monitor a live trading position with trailing stop-loss"""
        # Initialize stop-loss and take-profit levels
        initial_stop_loss = buy_price * (1 - self.stop_loss_percent / 100)
        take_profit = buy_price * (1 + self.take_profit_percent / 100)

        # Initialize trailing stop variables
        trailing_stop = initial_stop_loss
        highest_price = buy_price

        # Update pair statistics
        if symbol not in self.pair_performance:
            self.pair_performance[symbol] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}
        self.pair_performance[symbol]['trades'] += 1

        while True:
            try:
                current_price = float(self.client.get_symbol_ticker(symbol=symbol)['price'])
                current_time = datetime.datetime.now()
                time_in_trade = (current_time - buy_time).total_seconds() / 60  # minutes

                # Update highest price and trailing stop if price moves up
                if current_price > highest_price:
                    highest_price = current_price
                    # Only update trailing stop if it would be higher than the current one
                    new_trailing_stop = highest_price * (1 - self.trailing_stop_percent / 100)
                    if new_trailing_stop > trailing_stop:
                        trailing_stop = new_trailing_stop
                        logger.info(f"[UPDATE] New high: {highest_price} | Updated trailing stop: {trailing_stop}")

                # Calculate current profit/loss percentage
                current_pnl_pct = (current_price - buy_price) / buy_price * 100

                # Check exit conditions
                if current_price <= trailing_stop:
                    logger.info(f"[STOP] Trailing stop triggered at {current_price} ({current_pnl_pct:.2f}%)")
                    self._execute_sell_order(symbol, quantity, current_price, buy_price, buy_time, time_in_trade, "TRAILING_STOP")
                    break

                elif current_price >= take_profit:
                    logger.info(f"[PROFIT] Take profit hit at {current_price} ({current_pnl_pct:.2f}%)")
                    self._execute_sell_order(symbol, quantity, current_price, buy_price, buy_time, time_in_trade, "TAKE_PROFIT")
                    break

                # Force exit if trade has been open for too long (8 hours)
                elif time_in_trade > 480:  # 8 hours in minutes
                    logger.info(f"[TIME] Time-based exit after {time_in_trade:.1f} minutes")
                    self._execute_sell_order(symbol, quantity, current_price, buy_price, buy_time, time_in_trade, "TIME_EXIT")
                    break

                else:
                    logger.info(f"[MONITOR] {symbol} | Current: {current_price:.8f} | " +
                              f"P/L: {current_pnl_pct:.2f}% | Trailing Stop: {trailing_stop:.8f}")

                # Sleep to avoid API rate limits
                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"[ERROR] Position monitoring error: {e}")
                time.sleep(60)  # Wait longer on error

    def _execute_sell_order(self, symbol, quantity, sell_price, buy_price, buy_time, time_in_trade, exit_reason):
        """Execute a sell order and record the trade"""
        try:
            # Execute sell order
            sell_order = self.client.order_market_sell(symbol=symbol, quantity=quantity)
            actual_sell_price = float(sell_order['fills'][0]['price'])
            profit_loss = (actual_sell_price - buy_price) * quantity

            # Update statistics
            self.total_trades += 1
            if actual_sell_price > buy_price:
                self.profitable_trades += 1
                self.pair_performance[symbol]['wins'] += 1
            self.total_profit_loss += profit_loss
            self.pair_performance[symbol]['profit_loss'] += profit_loss

            # Log trade results
            logger.info(f"[SELL] Order executed: {actual_sell_price}")
            logger.info(f"Trade P/L: {profit_loss:.2f} USDT ({(actual_sell_price-buy_price)/buy_price*100:.2f}%)")
            logger.info(f"Time in trade: {time_in_trade:.1f} minutes | Exit reason: {exit_reason}")

            # Add to trade history
            trade_record = {
                'symbol': symbol,
                'buy_price': buy_price,
                'sell_price': actual_sell_price,
                'quantity': quantity,
                'profit_loss': profit_loss,
                'profit_loss_pct': (actual_sell_price-buy_price)/buy_price*100,
                'date': buy_time,
                'duration_minutes': time_in_trade,
                'exit_reason': exit_reason
            }
            self.trade_history.append(trade_record)

            # Save trade history
            self._save_trade_history()

            # Update daily profits
            today_str = buy_time.strftime('%Y-%m-%d')
            if today_str not in self.daily_profits:
                self.daily_profits[today_str] = 0.0
            self.daily_profits[today_str] += profit_loss

        except Exception as e:
            logger.error(f"[ERROR] Sell order execution error: {e}")





    def _generate_performance_report(self):
        """Generate a performance report"""
        logger.info("\n===== PERFORMANCE REPORT =====")
        logger.info(f"Trading period: {self.start_date.strftime('%Y-%m-%d')} to {datetime.datetime.now().strftime('%Y-%m-%d')}")
        logger.info(f"Total trades: {self.total_trades}")

        if self.total_trades > 0:
            win_rate = self.profitable_trades / self.total_trades * 100
            logger.info(f"Win rate: {win_rate:.1f}% ({self.profitable_trades}/{self.total_trades})")
            logger.info(f"Total P/L: {self.total_profit_loss:.2f} USDT ({self.total_profit_loss/self.investment_amount*100:.1f}%)")

            # Calculate average profit per trade
            avg_profit = self.total_profit_loss / self.total_trades
            logger.info(f"Average P/L per trade: {avg_profit:.2f} USDT ({avg_profit/self.investment_amount*100:.1f}%)")

            # Best performing pair
            if self.pair_performance:
                best_pair = max(self.pair_performance.items(), key=lambda x: x[1]['profit_loss'])
                logger.info(f"Best performing pair: {best_pair[0]} with {best_pair[1]['profit_loss']:.2f} USDT profit")

            # Calculate daily returns
            if self.daily_profits:
                # Average daily return
                avg_daily_return = sum(self.daily_profits.values()) / len(self.daily_profits)
                logger.info(f"Average daily return: {avg_daily_return:.2f} USDT ({avg_daily_return/self.investment_amount*100:.1f}%)")

                # Best day
                best_day = max(self.daily_profits.items(), key=lambda x: x[1])
                logger.info(f"Best day: {best_day[0]} with {best_day[1]:.2f} USDT profit")
        else:
            logger.info("No trades executed during the period.")

        logger.info("==============================\n")

        # Save performance data
        self._save_performance_data()

    def _save_performance_data(self):
        """Save performance data to file"""
        performance_file = self.settings.get('paths', {}).get('performance_data', 'data/performance_data.json')

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(performance_file), exist_ok=True)

            # Prepare performance data
            performance_data = {
                'timestamp': datetime.datetime.now().timestamp(),
                'datetime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_trades': self.total_trades,
                'profitable_trades': self.profitable_trades,
                'total_profit_loss': self.total_profit_loss,
                'win_rate': (self.profitable_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                'pair_performance': self.pair_performance,
                'daily_profits': self.daily_profits
            }

            with open(performance_file, 'w') as f:
                json.dump(performance_data, f, indent=2)

            logger.debug("Saved performance data to file")

        except Exception as e:
            logger.error(f"Error saving performance data: {e}")

    def _start_web_monitor(self):
        """Start the web monitor in a separate thread"""
        try:
            # Import here to avoid circular imports
            from web.monitor import start_web_monitor

            # Get web monitor settings
            web_config = self.settings.get('web_monitor')

            # Start web monitor in a separate thread
            web_thread = threading.Thread(
                target=start_web_monitor,
                args=(web_config.get('host', '0.0.0.0'), web_config.get('port', 5000)),
                daemon=True
            )
            web_thread.start()

            logger.info(f"Web monitor started on http://{web_config.get('host', '0.0.0.0')}:{web_config.get('port', 5000)}")

        except Exception as e:
            logger.error(f"Failed to start web monitor: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Enhanced Trading Bot')
    parser.add_argument('--amount', type=float, help='Investment amount in USDT')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    args = parser.parse_args()

    # Get investment amount from command line or prompt
    investment_amount = args.amount
    if investment_amount is None:
        try:
            user_input = input("Enter investment amount in USDT (default: 30): ")
            investment_amount = float(user_input) if user_input.strip() else 30.0
        except ValueError:
            print("Invalid input. Using default amount: 30.0 USDT")
            investment_amount = 30.0

    # Initialize and start the bot
    bot = TradingBot(investment_amount)
    bot.start()

if __name__ == "__main__":
    main()
