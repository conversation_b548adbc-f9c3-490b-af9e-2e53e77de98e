"""
Trading Bot Main Module

This is the main entry point for the enhanced trading bot.
It integrates all components and provides a unified interface.

Features:
- Dynamic coin selection based on Binance trends
- Advanced technical analysis for trade signals
- Comprehensive risk management
- Performance tracking and reporting
- Web-based monitoring interface
- Automatic backups and notifications
"""

import os
import sys
import time
import json
import logging
import datetime
import threading
import argparse
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import modules
from config.settings import get_settings
from modules.coin_analyzer import get_selected_coins
from modules.paper_trading import get_paper_trading_engine
from utils.backup import get_backup_manager
from utils.notifications import get_notification_manager

# Configure logging
def setup_logging():
    """Set up logging configuration"""
    settings = get_settings()
    log_config = settings.get('logging')
    
    log_level = getattr(logging, log_config.get('level', 'INFO'))
    log_file = log_config.get('file', 'logs/trading_log.txt')
    
    # Ensure log directory exists
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

class TradingBot:
    """Main Trading Bot class that integrates all components"""
    
    def __init__(self, investment_amount=None):
        """
        Initialize the trading bot
        
        Args:
            investment_amount: Amount to invest per trade (in USDT)
        """
        logger.info("Initializing Trading Bot...")
        
        # Load settings
        self.settings = get_settings()
        
        # Load API keys from .env
        load_dotenv()
        self.api_key = os.getenv("BINANCE_API_KEY")
        self.api_secret = os.getenv("BINANCE_API_SECRET")
        
        if not self.api_key or not self.api_secret:
            logger.error("API keys not found. Please set BINANCE_API_KEY and BINANCE_API_SECRET in .env file")
            sys.exit(1)
        
        # Initialize Binance client
        self.client = Client(self.api_key, self.api_secret)
        
        # Check if paper trading mode is enabled
        self.paper_trading_enabled = (
            self.settings.get('trading', {}).get('paper_trading', False) or 
            self.settings.get('advanced', {}).get('paper_trading_mode', False)
        )
        
        # Initialize paper trading engine if enabled
        if self.paper_trading_enabled:
            paper_config = {
                'paper_balance': self.settings.get('trading', {}).get('paper_balance', 1000.0),
                'commission_rate': 0.001,  # 0.1% commission
                'slippage_percent': self.settings.get('advanced', {}).get('slippage_percent', 0.1),
                'simulate_slippage': self.settings.get('advanced', {}).get('simulate_slippage', True),
                'portfolio_file': 'data/paper_portfolio.json',
                'trades_file': 'data/paper_trades.json'
            }
            self.paper_engine = get_paper_trading_engine(paper_config)
            logger.info(f"Paper trading mode enabled with {paper_config['paper_balance']:.2f} USDT virtual balance")
        else:
            self.paper_engine = None
            logger.info("Live trading mode enabled")
        
        # Set investment amount
        trading_settings = self.settings.get('trading')
        self.investment_amount = investment_amount or trading_settings.get('default_investment')
        logger.info(f"Using investment amount: {self.investment_amount} USDT")
        
        # Initialize components
        self._init_components()
        
        # Trading parameters
        self.stop_loss_percent = trading_settings.get('stop_loss_percent')
        self.take_profit_percent = trading_settings.get('take_profit_percent')
        self.trailing_stop_percent = trading_settings.get('trailing_stop_percent')
        self.max_trades_per_day = trading_settings.get('max_trades_per_day')
        self.investment_period_days = trading_settings.get('investment_period_days')
        self.api_cooldown = trading_settings.get('api_cooldown')
        
        # Trading state
        self.start_date = datetime.datetime.now()
        self.end_date = self.start_date + datetime.timedelta(days=self.investment_period_days)
        self.total_trades = 0
        self.profitable_trades = 0
        self.total_profit_loss = 0.0
        self.trade_history = []
        self.pair_performance = {}
        self.daily_profits = {}
        self.active_trades = {}
        
        # Load trading history if available
        self._load_trade_history()
        
        # Initialize trading pairs
        self.pairs = []
        self._update_trading_pairs()
        
        logger.info(f"Trading Bot initialized with {len(self.pairs)} pairs")
        logger.info(f"Trading period: {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")
        logger.info(f"Target profit: {self.take_profit_percent}% per trade")
    
    def _init_components(self):
        """Initialize bot components"""
        # Initialize backup manager
        backup_config = {
            'backup_dir': self.settings.get('paths', {}).get('backup_dir', 'data/backups'),
            'max_backups': 10,
            'backup_interval': 86400,  # 24 hours
            'files_to_backup': [
                self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json'),
                self.settings.get('paths', {}).get('selected_coins', 'data/selected_coins.json'),
                self.settings.get('paths', {}).get('performance_data', 'data/performance_data.json'),
                'config/config.json',
                self.settings.get('logging', {}).get('file', 'logs/trading_log.txt')
            ]
        }
        self.backup_manager = get_backup_manager(backup_config)
        
        # Initialize notification manager
        self.notification_manager = get_notification_manager(self.settings.get('notifications'))
    
    def _load_trade_history(self):
        """Load trade history from file"""
        history_file = self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json')
        
        try:
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    self.trade_history = json.load(f)
                
                # Calculate statistics from history
                self.total_trades = len(self.trade_history)
                self.profitable_trades = sum(1 for trade in self.trade_history if trade.get('profit_loss', 0) > 0)
                self.total_profit_loss = sum(trade.get('profit_loss', 0) for trade in self.trade_history)
                
                # Calculate pair performance
                for trade in self.trade_history:
                    symbol = trade.get('symbol')
                    if symbol not in self.pair_performance:
                        self.pair_performance[symbol] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}
                    
                    self.pair_performance[symbol]['trades'] += 1
                    if trade.get('profit_loss', 0) > 0:
                        self.pair_performance[symbol]['wins'] += 1
                    self.pair_performance[symbol]['profit_loss'] += trade.get('profit_loss', 0)
                
                # Calculate daily profits
                for trade in self.trade_history:
                    if isinstance(trade.get('date'), str):
                        date = datetime.datetime.strptime(trade.get('date'), '%Y-%m-%d %H:%M:%S').date()
                    else:
                        date = trade.get('date').date()
                    
                    date_str = date.strftime('%Y-%m-%d')
                    if date_str not in self.daily_profits:
                        self.daily_profits[date_str] = 0.0
                    
                    self.daily_profits[date_str] += trade.get('profit_loss', 0)
                
                logger.info(f"Loaded {self.total_trades} trades from history")
                logger.info(f"Total P/L: {self.total_profit_loss:.2f} USDT")
                
        except Exception as e:
            logger.error(f"Error loading trade history: {e}")
    
    def _save_trade_history(self):
        """Save trade history to file"""
        history_file = self.settings.get('paths', {}).get('trade_history', 'data/trade_history.json')
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(history_file), exist_ok=True)
            
            # Convert datetime objects to strings
            serializable_history = []
            for trade in self.trade_history:
                trade_copy = trade.copy()
                if isinstance(trade_copy.get('date'), datetime.datetime):
                    trade_copy['date'] = trade_copy['date'].strftime('%Y-%m-%d %H:%M:%S')
                serializable_history.append(trade_copy)
            
            with open(history_file, 'w') as f:
                json.dump(serializable_history, f, indent=2)
            
            logger.debug(f"Saved {len(serializable_history)} trades to history file")
            
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")
    
    def _update_trading_pairs(self):
        """Update the list of trading pairs based on market analysis"""
        try:
            # Get coin selection configuration
            coin_config = self.settings.get('coin_selection')
            
            # Get selected coins from analyzer
            self.pairs = get_selected_coins(self.client, coin_config)
            
            if not self.pairs:
                logger.warning("No coins selected by analyzer, using default pairs")
                self.pairs = [
                    "INJUSDT",   # Injective - DeFi with high volatility
                    "FETUSDT",   # Fetch.ai - AI token with growth potential
                    "AGIXUSDT",  # SingularityNET - AI token with volatility
                    "APTUSDT",   # Aptos - Layer 1 with strong momentum
                    "SUIUSDT",   # Sui - New L1 blockchain with volatility
                    "PEPEUSDT",  # Pepe - Meme coin with high volatility
                    "FLOKIUSDT"  # Floki - Meme coin with high volatility
                ]
            
            logger.info(f"Updated trading pairs: {', '.join(self.pairs)}")
            
        except Exception as e:
            logger.error(f"Error updating trading pairs: {e}")
            # Use default pairs if there's an error
            self.pairs = [
                "INJUSDT",   # Injective - DeFi with high volatility
                "FETUSDT",   # Fetch.ai - AI token with growth potential
                "AGIXUSDT",  # SingularityNET - AI token with volatility
                "APTUSDT",   # Aptos - Layer 1 with strong momentum
                "SUIUSDT",   # Sui - New L1 blockchain with volatility
                "PEPEUSDT",  # Pepe - Meme coin with high volatility
                "FLOKIUSDT"  # Floki - Meme coin with high volatility
            ]
    
    def start(self):
        """Start the trading bot"""
        logger.info("Starting trading bot main loop...")
        
        try:
            # Create initial backup
            self.backup_manager.create_backup()
            
            # Start web monitor in a separate thread if enabled
            if self.settings.get('web_monitor', {}).get('enabled', True):
                self._start_web_monitor()
            
            # Main trading loop
            while datetime.datetime.now() < self.end_date:
                # Check account balance
                self._check_account_balance()
                
                # Update trading pairs periodically
                if datetime.datetime.now().minute % 30 == 0:  # Every 30 minutes
                    self._update_trading_pairs()
                
                # Scan all pairs for trading opportunities
                for pair in self.pairs:
                    # Update pair performance tracking
                    if pair not in self.pair_performance:
                        self.pair_performance[pair] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}
                    
                    # Execute trade function
                    self._analyze_and_trade(pair)
                    
                    # Sleep between pairs to avoid API rate limits
                    time.sleep(self.api_cooldown)
                
                # Generate daily report at midnight
                if datetime.datetime.now().hour == 0 and datetime.datetime.now().minute < 5:
                    self._generate_performance_report()
                    # Create daily backup
                    self.backup_manager.create_backup()
                
                logger.info("[CYCLE] Scan completed. Waiting before next scan...")
                time.sleep(300)  # Wait 5 minutes between full scans
            
            # Generate final report at the end of the investment period
            logger.info("Investment period completed!")
            self._generate_performance_report()
            
        except KeyboardInterrupt:
            logger.info("Bot stopped by user.")
            self._generate_performance_report()
        except Exception as e:
            logger.error(f"Critical error: {e}")
            self._generate_performance_report()
    
    def _check_account_balance(self):
        """Check if there's enough balance for trading"""
        try:
            if self.paper_trading_enabled:
                # For paper trading, check virtual balance
                if self.paper_engine.balance < self.investment_amount:
                    logger.warning(f"Insufficient paper trading balance: {self.paper_engine.balance:.2f}. Required: {self.investment_amount}")
                    return False
                return True
            else:
                # For live trading, check actual Binance balance
                account = self.client.get_account()
                usdt_balance = float(next((asset['free'] for asset in account['balances'] if asset['asset'] == 'USDT'), 0))
                
                if usdt_balance < self.investment_amount:
                    logger.warning(f"Insufficient USDT balance: {usdt_balance}. Required: {self.investment_amount}")
                    logger.info("Waiting for 30 minutes before checking again...")
                    time.sleep(1800)  # Wait 30 minutes
                    return False
                
                return True
            
        except Exception as e:
            logger.error(f"Error checking account balance: {e}")
            return False
    
    def _analyze_and_trade(self, symbol):
        """
        Analyze a symbol and execute a trade if conditions are met
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
        """
        # This is a placeholder for the actual trading logic
        # The full implementation would include the signal generation, trade execution,
        # and position management code from fast_trading_bot.py
        
        logger.info(f"[SCAN] Analyzing {symbol}...")
        
        # TODO: Implement the full trading logic here
        # This would include:
        # 1. Getting klines data
        # 2. Calculating indicators
        # 3. Generating signals
        # 4. Executing trades
        # 5. Managing positions with trailing stop-loss
        
        # For now, we'll just log that we're analyzing the symbol
        logger.info(f"[INFO] No strong entry signal for {symbol}")
    
    def _generate_performance_report(self):
        """Generate a performance report"""
        logger.info("\n===== PERFORMANCE REPORT =====")
        logger.info(f"Trading period: {self.start_date.strftime('%Y-%m-%d')} to {datetime.datetime.now().strftime('%Y-%m-%d')}")
        logger.info(f"Total trades: {self.total_trades}")
        
        if self.total_trades > 0:
            win_rate = self.profitable_trades / self.total_trades * 100
            logger.info(f"Win rate: {win_rate:.1f}% ({self.profitable_trades}/{self.total_trades})")
            logger.info(f"Total P/L: {self.total_profit_loss:.2f} USDT ({self.total_profit_loss/self.investment_amount*100:.1f}%)")
            
            # Calculate average profit per trade
            avg_profit = self.total_profit_loss / self.total_trades
            logger.info(f"Average P/L per trade: {avg_profit:.2f} USDT ({avg_profit/self.investment_amount*100:.1f}%)")
            
            # Best performing pair
            if self.pair_performance:
                best_pair = max(self.pair_performance.items(), key=lambda x: x[1]['profit_loss'])
                logger.info(f"Best performing pair: {best_pair[0]} with {best_pair[1]['profit_loss']:.2f} USDT profit")
            
            # Calculate daily returns
            if self.daily_profits:
                # Average daily return
                avg_daily_return = sum(self.daily_profits.values()) / len(self.daily_profits)
                logger.info(f"Average daily return: {avg_daily_return:.2f} USDT ({avg_daily_return/self.investment_amount*100:.1f}%)")
                
                # Best day
                best_day = max(self.daily_profits.items(), key=lambda x: x[1])
                logger.info(f"Best day: {best_day[0]} with {best_day[1]:.2f} USDT profit")
        else:
            logger.info("No trades executed during the period.")
        
        logger.info("==============================\n")
        
        # Save performance data
        self._save_performance_data()
    
    def _save_performance_data(self):
        """Save performance data to file"""
        performance_file = self.settings.get('paths', {}).get('performance_data', 'data/performance_data.json')
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(performance_file), exist_ok=True)
            
            # Prepare performance data
            performance_data = {
                'timestamp': datetime.datetime.now().timestamp(),
                'datetime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_trades': self.total_trades,
                'profitable_trades': self.profitable_trades,
                'total_profit_loss': self.total_profit_loss,
                'win_rate': (self.profitable_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                'pair_performance': self.pair_performance,
                'daily_profits': self.daily_profits
            }
            
            with open(performance_file, 'w') as f:
                json.dump(performance_data, f, indent=2)
            
            logger.debug("Saved performance data to file")
            
        except Exception as e:
            logger.error(f"Error saving performance data: {e}")
    
    def _start_web_monitor(self):
        """Start the web monitor in a separate thread"""
        try:
            # Import here to avoid circular imports
            from web.monitor import start_web_monitor
            
            # Get web monitor settings
            web_config = self.settings.get('web_monitor')
            
            # Start web monitor in a separate thread
            web_thread = threading.Thread(
                target=start_web_monitor,
                args=(web_config.get('host', '0.0.0.0'), web_config.get('port', 5000)),
                daemon=True
            )
            web_thread.start()
            
            logger.info(f"Web monitor started on http://{web_config.get('host', '0.0.0.0')}:{web_config.get('port', 5000)}")
            
        except Exception as e:
            logger.error(f"Failed to start web monitor: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Enhanced Trading Bot')
    parser.add_argument('--amount', type=float, help='Investment amount in USDT')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    args = parser.parse_args()
    
    # Get investment amount from command line or prompt
    investment_amount = args.amount
    if investment_amount is None:
        try:
            user_input = input("Enter investment amount in USDT (default: 30): ")
            investment_amount = float(user_input) if user_input.strip() else 30.0
        except ValueError:
            print("Invalid input. Using default amount: 30.0 USDT")
            investment_amount = 30.0
    
    # Initialize and start the bot
    bot = TradingBot(investment_amount)
    bot.start()

if __name__ == "__main__":
    main()
