#!/usr/bin/env python3
"""
Set Paper Balance Helper Script
Used by auto_trading_mode.bat to set paper trading balance
"""

import sys
import json
import os

def set_paper_balance(balance):
    """Set paper trading balance in configuration"""
    config_file = 'config/auto_mode/schedule.json'
    
    try:
        balance = float(balance)
        
        # Read current configuration
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
        else:
            config = {}
        
        # Update paper balance setting
        config['paper_balance'] = balance
        
        # Save configuration
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Also update main config
        main_config_file = 'config/config.json'
        if os.path.exists(main_config_file):
            with open(main_config_file, 'r') as f:
                main_config = json.load(f)
            
            main_config['trading']['paper_balance'] = balance
            
            with open(main_config_file, 'w') as f:
                json.dump(main_config, f, indent=2)
        
        print(f"Successfully set paper trading balance to {balance:.2f} USDT")
        
    except ValueError:
        print("Error: Invalid balance amount. Please enter a number.")
    except Exception as e:
        print(f"Error setting paper balance: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python set_paper_balance.py <balance>")
        sys.exit(1)
    
    set_paper_balance(sys.argv[1]) 