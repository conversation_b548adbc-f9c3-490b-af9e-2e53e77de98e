#!/usr/bin/env python3
"""
Simple test to verify configuration works
"""

import os
import subprocess
import json

def main():
    print("Testing configuration system...")
    
    # Test 1: Run read_config.py
    print("\n1. Testing read_config.py...")
    result = subprocess.run(['python', 'read_config.py'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✓ read_config.py executed successfully")
    else:
        print(f"❌ read_config.py failed: {result.stderr}")
        return False
    
    # Test 2: Check temp file
    print("\n2. Checking temp file...")
    if os.path.exists('config/auto_mode/temp.txt'):
        print("✓ temp.txt exists")
        with open('config/auto_mode/temp.txt', 'r') as f:
            content = f.read()
        print(f"✓ Content: {len(content.splitlines())} lines")
        
        # Parse the content
        config_vars = {}
        for line in content.splitlines():
            if '=' in line and line.strip():
                key, value = line.strip().split('=', 1)
                config_vars[key] = value
        
        print("✓ Parsed variables:")
        for key, value in config_vars.items():
            print(f"  {key}={value}")
    else:
        print("❌ temp.txt not found")
        return False
    
    # Test 3: Test toggle paper trading
    print("\n3. Testing toggle paper trading...")
    result = subprocess.run(['python', 'toggle_paper_trading.py', 'true'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✓ toggle_paper_trading.py works")
        print(f"✓ Output: {result.stdout.strip()}")
    else:
        print(f"❌ toggle_paper_trading.py failed: {result.stderr}")
        return False
    
    # Test 4: Check config files
    print("\n4. Checking config files...")
    
    # Check auto mode config
    if os.path.exists('config/auto_mode/schedule.json'):
        with open('config/auto_mode/schedule.json', 'r') as f:
            auto_config = json.load(f)
        print(f"✓ Auto config paper_trading: {auto_config.get('paper_trading')}")
    else:
        print("❌ Auto mode config not found")
        return False
    
    # Check main config
    if os.path.exists('config/config.json'):
        with open('config/config.json', 'r') as f:
            main_config = json.load(f)
        print(f"✓ Main config paper_trading: {main_config.get('trading', {}).get('paper_trading')}")
    else:
        print("❌ Main config not found")
        return False
    
    print("\n🎉 ALL TESTS PASSED!")
    print("\nThe auto_trading_mode.bat should now work correctly.")
    print("You can now:")
    print("1. Run auto_trading_mode.bat")
    print("2. Press 2 to toggle between Paper/Live trading")
    print("3. Press B to start auto mode")
    print("4. Web monitor will start automatically")
    
    return True

if __name__ == "__main__":
    main()
