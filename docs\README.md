# Enhanced Crypto Trading Bot

A sophisticated trading bot designed to identify and execute high-volatility cryptocurrency trades on Binance with the goal of achieving 15-30% returns.

## Key Features

- **Dynamic Coin Selection**: Automatically analyzes and selects the most promising coins from Binance trends
- **Advanced Technical Analysis**: Uses multiple indicators across different timeframes for signal confirmation
- **Trailing Stop-Loss**: Locks in profits as price moves in your favor
- **Comprehensive Risk Management**: Limits daily trades and implements proper position sizing
- **Web Monitoring Interface**: Real-time dashboard for tracking performance
- **Automatic Dependency Management**: Checks for and installs required packages
- **Performance Tracking**: Detailed logs and reports on trading performance
- **Backup System**: Automatically backs up important data

## Directory Structure

```
Trading Bot/
├── config/               # Configuration files
│   ├── config.json       # User configuration
│   └── settings.py       # Settings management
├── data/                 # Data storage
│   ├── trade_history.json    # Record of all trades
│   ├── selected_coins.json   # Currently selected coins
│   ├── performance_data.json # Performance metrics
│   ├── coin_analysis.json    # Coin analysis results
│   └── backups/          # Automatic backups
├── logs/                 # Log files
│   └── trading_log.txt   # Detailed activity log
├── modules/              # Core functionality
│   └── coin_analyzer.py  # Coin selection and analysis
├── utils/                # Utility functions
│   ├── backup.py         # Backup management
│   └── notifications.py  # Email/Telegram notifications
├── web/                  # Web interface
│   ├── monitor.py        # Web server
│   ├── templates/        # HTML templates
│   └── static/           # Static assets
├── main.py               # Main entry point
├── start_trading.bat     # Startup script
├── stop_trading.bat      # Shutdown script
├── requirements.txt      # Python dependencies
└── README.md             # Documentation
```

## Quick Start

1. **Run the Startup Script**:
   ```
   start_trading.bat
   ```

2. **Enter Investment Amount**:
   When prompted, enter your desired investment amount in USDT (default: 30 USDT)

3. **Monitor Performance**:
   The web interface will automatically open in your browser at http://localhost:5000

4. **Stop the Bot**:
   ```
   stop_trading.bat
   ```

## Prerequisites

- Python 3.7+
- Binance account with API access
- Internet connection

The startup script will automatically check for and install all required dependencies.

## Dynamic Coin Selection

The bot automatically selects the most promising coins for trading based on:

1. **Market Trends**: Analyzes Binance's top trending coins
2. **Volume Analysis**: Prioritizes coins with high trading volume
3. **Volatility Metrics**: Focuses on coins with price volatility suitable for 15-30% moves
4. **Technical Patterns**: Identifies coins with favorable technical setups
5. **Multi-Timeframe Confirmation**: Verifies signals across different timeframes

The coin selection is updated regularly to adapt to changing market conditions.

## Web Monitoring Interface

The web interface provides comprehensive monitoring capabilities:

- **Performance Summary**: Total trades, win rate, profits, and losses
- **Active Trades**: Currently open positions with real-time P/L
- **Daily Performance**: Trades, profits, and losses per day
- **Completed Trades**: History of all executed trades
- **Selected Coins**: Currently monitored coins
- **Recent Logs**: Latest activity logs

The dashboard automatically refreshes every 30 seconds to show the latest data.

## Configuration

The bot is highly configurable through the `config/config.json` file:

- **Trading Parameters**: Stop-loss, take-profit, trailing stop percentages
- **Coin Selection**: Volume thresholds, update intervals, blacklisted coins
- **Signal Generation**: Minimum score, timeframes to analyze
- **Web Monitor**: Host, port, refresh interval
- **Logging**: Log level, file paths, rotation settings
- **Notifications**: Email and Telegram alert settings

## Trading Strategy

The bot uses a scoring system (0-12 points) based on multiple technical indicators:

- **Trend indicators**: EMA crossovers and price action
- **Momentum indicators**: RSI and Stochastic Oscillator
- **Volatility indicators**: Bollinger Bands
- **Volume analysis**: Volume spikes and trends

A trade is only executed when:
1. The signal strength score is 7 or higher (out of 12)
2. Signals are confirmed on both 5-minute and 15-minute timeframes

## Advanced Features

### Automatic Backups
The bot automatically creates backups of important data files:
- Trade history
- Performance metrics
- Configuration settings
- Log files

Backups are stored in the `data/backups` directory with timestamps.

### Notifications System
The bot includes support for email and Telegram notifications:
- Trade entries and exits
- Significant profits or losses
- Daily performance summaries
- Error conditions

To enable notifications, configure the settings in `config/config.json`.

## Risk Warning

**IMPORTANT**: This bot is designed for high-risk, high-reward trading. The target of 15-30% daily returns involves significant risk and is not sustainable long-term. Only use this bot with capital you can afford to lose entirely.

## Realistic Expectations

While the bot targets 15-30% returns, it's important to understand:

1. **Market Reality**: Even professional traders typically achieve 1-3% monthly returns consistently
2. **Risk Level**: High returns necessarily involve high risk
3. **Market Conditions**: Performance will vary significantly based on market conditions
4. **Learning Tool**: This bot is an excellent educational tool for algorithmic trading

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is for educational purposes only. Use at your own risk. The authors are not responsible for any financial losses incurred from using this software. Cryptocurrency trading involves significant risk and may not be suitable for everyone.
