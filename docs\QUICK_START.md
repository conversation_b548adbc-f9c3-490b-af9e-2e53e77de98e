# Quick Start Guide

This guide provides the essential steps to get your trading bot up and running quickly.

## Setup (One-Time)

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Keys**
   Create a `.env` file with your Binance API credentials:
   ```
   BINANCE_API_KEY=your_api_key_here
   BINANCE_API_SECRET=your_api_secret_here
   ```

## Starting the Bot

1. **Run the Trading Bot**
   ```bash
   python fast_trading_bot.py
   ```

2. **Enter Investment Amount**
   When prompted, enter your desired trade amount (default: 30 USDT)
   ```
   Enter trade amount in USDT (default: 30.0):
   ```

3. **Start the Web Monitor** (in a separate terminal)
   ```bash
   python trade_monitor.py
   ```

4. **Access the Dashboard**
   Open your browser and go to:
   ```
   http://localhost:5000
   ```

## Dashboard Overview

The web interface shows:

- **Performance Summary**: Total trades, win rate, profits, and losses
- **Active Trades**: Currently open positions
- **Daily Performance**: Trades, profits, and losses per day
- **Completed Trades**: History of all executed trades

## Key Features

- Targets 15-30% returns using advanced technical analysis
- Monitors 7 high-volatility cryptocurrency pairs
- Uses trailing stop-loss to maximize profits
- Provides comprehensive performance tracking
- Automatically generates daily reports

## Default Settings

- **Investment**: 30 USDT per trade
- **Take Profit**: 15%
- **Stop Loss**: 5%
- **Trailing Stop**: 3%
- **Trading Period**: 7 days

For detailed instructions, see the [User Guide](USER_GUIDE.md) and [Documentation](DOCUMENTATION.md).
