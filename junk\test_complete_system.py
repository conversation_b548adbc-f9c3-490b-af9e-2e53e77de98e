#!/usr/bin/env python3
"""
Complete system test for the trading bot
"""

import os
import sys
import json
import time
import threading
import subprocess

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_module():
    """Test main trading bot module"""
    print("Testing main trading bot module...")
    try:
        import main
        print("✓ Main module imports successfully")
        
        # Test TradingBot class instantiation
        if not os.path.exists('.env'):
            # Create test .env file
            with open('.env', 'w') as f:
                f.write("BINANCE_API_KEY=test_key\nBINANCE_API_SECRET=test_secret\nPAPER_TRADING=true\n")
        
        # Test bot initialization (this should work with paper trading)
        bot = main.TradingBot(investment_amount=30.0)
        print("✓ TradingBot initialized successfully")
        print(f"✓ Paper trading mode: {bot.paper_trading_enabled}")
        print(f"✓ Investment amount: {bot.investment_amount} USDT")
        
        return True
    except Exception as e:
        print(f"❌ Main module test failed: {e}")
        return False

def test_auto_mode_runner():
    """Test auto mode runner"""
    print("\nTesting auto mode runner...")
    try:
        import auto_mode_runner
        print("✓ Auto mode runner imports successfully")
        
        # Test configuration loading
        config = auto_mode_runner.load_config()
        print(f"✓ Configuration loaded: {config}")
        
        return True
    except Exception as e:
        print(f"❌ Auto mode runner test failed: {e}")
        return False

def test_web_monitor():
    """Test web monitor"""
    print("\nTesting web monitor...")
    try:
        from web.monitor import create_templates, update_trade_data
        
        # Create templates
        create_templates()
        print("✓ Web templates created")
        
        # Test data update
        update_trade_data()
        print("✓ Trade data update successful")
        
        return True
    except Exception as e:
        print(f"❌ Web monitor test failed: {e}")
        return False

def test_trading_days_config():
    """Test trading days configuration"""
    print("\nTesting trading days configuration...")
    try:
        # Test set_days.py script
        result = subprocess.run([
            'python', 'set_days.py', 
            'Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ Trading days configuration successful")
            print(f"✓ Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Trading days configuration failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Trading days test failed: {e}")
        return False

def test_complete_startup():
    """Test complete system startup simulation"""
    print("\nTesting complete system startup...")
    try:
        # Start web monitor in background
        print("Starting web monitor...")
        web_process = subprocess.Popen([
            'python', '-c', 
            'from web.monitor import create_templates, start_web_monitor; create_templates(); start_web_monitor("0.0.0.0", 5001)'
        ])
        
        # Wait for web monitor to start
        time.sleep(3)
        
        # Test if web monitor is accessible
        try:
            import requests
            response = requests.get('http://localhost:5001', timeout=5)
            if response.status_code == 200:
                print("✓ Web monitor is accessible")
            else:
                print(f"⚠️  Web monitor returned status: {response.status_code}")
        except:
            print("⚠️  Could not test web monitor accessibility (requests not available)")
        
        # Terminate web process
        web_process.terminate()
        web_process.wait(timeout=5)
        
        print("✓ Complete startup test successful")
        return True
        
    except Exception as e:
        print(f"❌ Complete startup test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("COMPLETE TRADING BOT SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Main Module Test", test_main_module),
        ("Auto Mode Runner Test", test_auto_mode_runner),
        ("Web Monitor Test", test_web_monitor),
        ("Trading Days Config Test", test_trading_days_config),
        ("Complete Startup Test", test_complete_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name}...")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ SYSTEM STATUS: FULLY FUNCTIONAL")
        print("\nNext steps:")
        print("1. Run 'auto_trading_mode.bat' to start the system")
        print("2. Press '8' to start auto trading")
        print("3. Web monitor will automatically open at http://localhost:5000")
        print("4. Monitor your trades in real-time")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    main()
