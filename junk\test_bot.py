#!/usr/bin/env python3
"""
Test script for the trading bot
This script tests the main components without executing real trades
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    try:
        import pandas as pd
        import ta
        from binance.client import Client
        from dotenv import load_dotenv
        print("✓ All basic imports successful")
        
        # Test project imports
        from config.settings import get_settings
        from modules.coin_analyzer import get_selected_coins
        from modules.paper_trading import get_paper_trading_engine
        print("✓ All project imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    try:
        from config.settings import get_settings
        settings = get_settings()
        
        # Check if config file exists
        if os.path.exists('config/config.json'):
            print("✓ Configuration file exists")
        else:
            print("⚠️  Configuration file not found, using defaults")
        
        # Test key settings
        trading_settings = settings.get('trading', {})
        print(f"✓ Default investment: {trading_settings.get('default_investment', 30)} USDT")
        print(f"✓ Paper trading: {trading_settings.get('paper_trading', True)}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_paper_trading():
    """Test paper trading engine"""
    print("\nTesting paper trading engine...")
    try:
        from modules.paper_trading import get_paper_trading_engine
        
        config = {
            'paper_balance': 1000.0,
            'commission_rate': 0.001,
            'slippage_percent': 0.1,
            'portfolio_file': 'data/test_paper_portfolio.json',
            'trades_file': 'data/test_paper_trades.json'
        }
        
        engine = get_paper_trading_engine(config)
        print(f"✓ Paper trading engine initialized with {engine.balance:.2f} USDT")
        
        # Test a paper trade
        result = engine.buy_market(
            symbol="BTCUSDT",
            quantity=0.001,
            price=50000.0,
            stop_loss=49000.0,
            take_profit=51000.0
        )
        
        if result['status'] == 'FILLED':
            print("✓ Paper trade execution successful")
        else:
            print(f"⚠️  Paper trade failed: {result.get('error', 'Unknown error')}")
        
        return True
    except Exception as e:
        print(f"❌ Paper trading error: {e}")
        return False

def test_directories():
    """Test required directories"""
    print("\nTesting directories...")
    required_dirs = ['config', 'data', 'logs', 'web/templates', 'web/static']
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path} exists")
        else:
            print(f"⚠️  Creating {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
    
    return True

def test_web_monitor():
    """Test web monitor components"""
    print("\nTesting web monitor...")
    try:
        from web.monitor import create_templates, update_trade_data
        
        # Create templates
        create_templates()
        print("✓ Web templates created")
        
        # Test data update
        update_trade_data()
        print("✓ Trade data update successful")
        
        return True
    except Exception as e:
        print(f"❌ Web monitor error: {e}")
        return False

def create_test_env():
    """Create a test .env file for paper trading"""
    print("\nCreating test environment...")
    
    if not os.path.exists('.env'):
        test_env_content = """# Test API Configuration (these are dummy keys for testing)
BINANCE_API_KEY=test_api_key_for_paper_trading
BINANCE_API_SECRET=test_api_secret_for_paper_trading

# Trading Mode (true for paper trading, false for live trading)
PAPER_TRADING=true

# Test mode flag
TEST_MODE=true
"""
        
        with open('.env', 'w') as f:
            f.write(test_env_content)
        
        print("✓ Test .env file created (paper trading mode)")
    else:
        print("✓ .env file already exists")
    
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("TRADING BOT SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Directory Test", test_directories),
        ("Environment Test", create_test_env),
        ("Configuration Test", test_config),
        ("Paper Trading Test", test_paper_trading),
        ("Web Monitor Test", test_web_monitor),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name}...")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The trading bot is ready to run.")
        print("\nNext steps:")
        print("1. Run 'python main.py --amount 30' to start paper trading")
        print("2. Open http://localhost:5000 to view the web monitor")
        print("3. For live trading, update your .env file with real API keys")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the bot.")
    
    return passed == total

if __name__ == "__main__":
    main()
