@echo off
setlocal enabledelayedexpansion
mode con: cols=80 lines=30
color 0A

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

:: Check if pip is installed
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: pip is not installed. Attempting to install pip...
    python -m ensurepip --default-pip
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install pip.
        echo Please install pip manually and try again.
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
    echo pip installed successfully.
)

:: Check for required packages
echo Checking for required packages...
set MISSING_PACKAGES=0

:: Check for python-binance
python -c "import binance" >nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for pandas
python -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for ta
python -c "import ta" >nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for python-dotenv
python -c "import dotenv" >nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Check for flask
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Install missing packages if needed
if %MISSING_PACKAGES% equ 1 (
    echo Some required packages are missing. Installing now...

    if exist requirements.txt (
        python -m pip install -r requirements.txt
    ) else (
        echo requirements.txt not found. Installing packages individually...
        python -m pip install python-binance pandas ta python-dotenv flask
    )

    if %errorlevel% neq 0 (
        echo ERROR: Failed to install required packages.
        echo Please install them manually:
        echo python -m pip install python-binance pandas ta python-dotenv flask
        echo.
        echo Press any key to continue anyway...
        pause >nul
    ) else (
        echo All required packages installed successfully.
    )
)

:: Check if .env file exists with API keys
if not exist .env (
    echo WARNING: .env file not found. API keys may be missing.
    echo The bot requires Binance API keys to function.
    echo.
    set /p CREATE_ENV="Would you like to create a .env file now? (Y/N): "
    if /i "!CREATE_ENV!"=="Y" (
        echo Creating .env file...
        echo.
        set /p API_KEY="Enter your Binance API key: "
        set /p API_SECRET="Enter your Binance API secret: "

        echo BINANCE_API_KEY=!API_KEY!> .env
        echo BINANCE_API_SECRET=!API_SECRET!>> .env

        echo .env file created successfully.
    )
)

:MENU
cls
echo ========================================================================
echo                        TRADING BOT MANAGER v1.0
echo ========================================================================
echo.
echo  Current Status:
call :CHECK_STATUS
echo.
echo  [1] Start Trading Bot (Default 30 USDT)
echo  [2] Start Trading Bot (Custom Amount)
echo  [3] Open Web Monitor
echo  [4] Stop All Services
echo  [5] View Logs
echo  [6] Check Performance
echo  [7] Configure Auto Mode
echo  [8] Exit
echo.
echo ========================================================================
echo.

set /p CHOICE="Enter your choice (1-8): "

if "%CHOICE%"=="1" goto START_DEFAULT
if "%CHOICE%"=="2" goto START_CUSTOM
if "%CHOICE%"=="3" goto OPEN_MONITOR
if "%CHOICE%"=="4" goto STOP_SERVICES
if "%CHOICE%"=="5" goto VIEW_LOGS
if "%CHOICE%"=="6" goto CHECK_PERFORMANCE
if "%CHOICE%"=="7" goto AUTO_MODE
if "%CHOICE%"=="8" goto EXIT

echo Invalid choice. Please try again.
timeout /t 2 > nul
goto MENU

:START_DEFAULT
call :STOP_SERVICES_SILENT
echo Starting Trading Bot with default amount (30 USDT)...
echo.

:: Create a temporary script to handle the input
echo import sys > temp_input.py
echo import time >> temp_input.py
echo import subprocess >> temp_input.py
echo. >> temp_input.py
echo # Start the trading bot with the default investment amount >> temp_input.py
echo process = subprocess.Popen(['python', 'main.py', '--amount', '30'], stdin=subprocess.PIPE, text=True) >> temp_input.py
echo process.stdin.write('30\n') >> temp_input.py
echo process.stdin.flush() >> temp_input.py
echo. >> temp_input.py
echo # Keep the process running >> temp_input.py
echo while process.poll() is None: >> temp_input.py
echo     try: >> temp_input.py
echo         time.sleep(1) >> temp_input.py
echo     except KeyboardInterrupt: >> temp_input.py
echo         process.terminate() >> temp_input.py
echo         break >> temp_input.py

:: Start the trading bot in a new window
start "Trading Bot" cmd /c "python temp_input.py"
timeout /t 3 > nul

:: Start the web monitor in a new window
echo Starting Web Monitor...
start "Web Monitor" cmd /c "python -c \"from web.monitor import start_web_monitor; start_web_monitor()\""
timeout /t 5 > nul

:: Open the web interface in the default browser
echo Opening Web Interface...
start http://localhost:5000

echo.
echo Trading Bot started successfully with 30 USDT investment.
echo Press any key to return to the menu...
pause > nul
goto MENU

:START_CUSTOM
call :STOP_SERVICES_SILENT
echo Starting Trading Bot with custom amount...
echo.
set /p AMOUNT="Enter investment amount in USDT: "

:: Create a temporary script to handle the input
echo import sys > temp_input.py
echo import time >> temp_input.py
echo import subprocess >> temp_input.py
echo. >> temp_input.py
echo # Start the trading bot with the specified investment amount >> temp_input.py
echo process = subprocess.Popen(['python', 'main.py', '--amount', '%AMOUNT%'], stdin=subprocess.PIPE, text=True) >> temp_input.py
echo process.stdin.write('%AMOUNT%\n') >> temp_input.py
echo process.stdin.flush() >> temp_input.py
echo. >> temp_input.py
echo # Keep the process running >> temp_input.py
echo while process.poll() is None: >> temp_input.py
echo     try: >> temp_input.py
echo         time.sleep(1) >> temp_input.py
echo     except KeyboardInterrupt: >> temp_input.py
echo         process.terminate() >> temp_input.py
echo         break >> temp_input.py

:: Start the trading bot in a new window
start "Trading Bot" cmd /c "python temp_input.py"
timeout /t 3 > nul

:: Start the web monitor in a new window
echo Starting Web Monitor...
start "Web Monitor" cmd /c "python -c \"from web.monitor import start_web_monitor; start_web_monitor()\""
timeout /t 5 > nul

:: Open the web interface in the default browser
echo Opening Web Interface...
start http://localhost:5000

echo.
echo Trading Bot started successfully with %AMOUNT% USDT investment.
echo Press any key to return to the menu...
pause > nul
goto MENU

:OPEN_MONITOR
echo Opening Web Monitor...
start http://localhost:5000
echo.
echo If the web monitor is not running, please start the trading bot first.
echo Press any key to return to the menu...
pause > nul
goto MENU

:STOP_SERVICES
echo Stopping all services...
taskkill /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1
taskkill /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1
taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
if exist temp_input.py del temp_input.py > nul 2>&1
echo.
echo All services stopped successfully.
echo Press any key to return to the menu...
pause > nul
goto MENU

:STOP_SERVICES_SILENT
taskkill /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1
taskkill /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1
taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
if exist temp_input.py del temp_input.py > nul 2>&1
exit /b

:VIEW_LOGS
cls
echo ========================================================================
echo                             LOG VIEWER
echo ========================================================================
echo.
echo  [1] View Trading Log
echo  [2] View Trade History
echo  [3] Back to Main Menu
echo.
echo ========================================================================
echo.

set /p LOG_CHOICE="Enter your choice (1-3): "

if "%LOG_CHOICE%"=="1" (
    if exist trading_log.txt (
        cls
        echo ========================================================================
        echo                             TRADING LOG
        echo ========================================================================
        echo.
        type trading_log.txt | more
        echo.
        echo Press any key to return...
        pause > nul
    ) else (
        echo Trading log file not found.
        timeout /t 2 > nul
    )
    goto VIEW_LOGS
)

if "%LOG_CHOICE%"=="2" (
    if exist trade_history.json (
        cls
        echo ========================================================================
        echo                           TRADE HISTORY
        echo ========================================================================
        echo.
        type trade_history.json | more
        echo.
        echo Press any key to return...
        pause > nul
    ) else (
        echo Trade history file not found.
        timeout /t 2 > nul
    )
    goto VIEW_LOGS
)

if "%LOG_CHOICE%"=="3" goto MENU

echo Invalid choice. Please try again.
timeout /t 2 > nul
goto VIEW_LOGS

:CHECK_PERFORMANCE
echo Opening Web Monitor for performance check...
start http://localhost:5000
echo.
echo Press any key to return to the menu...
pause > nul
goto MENU

:CHECK_STATUS
set BOT_RUNNING=0
set MONITOR_RUNNING=0

tasklist /FI "WINDOWTITLE eq Trading Bot*" 2>nul | find "cmd.exe" >nul
if %errorlevel% equ 0 set BOT_RUNNING=1

tasklist /FI "WINDOWTITLE eq Web Monitor*" 2>nul | find "cmd.exe" >nul
if %errorlevel% equ 0 set MONITOR_RUNNING=1

if %BOT_RUNNING% equ 1 (
    echo  - Trading Bot: [RUNNING]
) else (
    echo  - Trading Bot: [STOPPED]
)

if %MONITOR_RUNNING% equ 1 (
    echo  - Web Monitor: [RUNNING]
) else (
    echo  - Web Monitor: [STOPPED]
)

exit /b

:AUTO_MODE
echo Starting Auto Mode Configuration...
call auto_trading_mode.bat
goto MENU

:EXIT
if exist temp_input.py del temp_input.py > nul 2>&1
echo Exiting Trading Bot Manager...
timeout /t 2 > nul
exit

endlocal
