# Trading Bot Batch Scripts

This document explains the batch scripts provided for easy management of the trading bot.

## Key Features

All scripts include the following automated checks and setup features:

1. **Python Installation Check**: Verifies Python is installed and accessible
2. **Pip Installation Check**: Verifies pip is installed, attempts to install it if missing
3. **Dependency Management**: Checks for all required packages and installs them if missing
4. **API Key Setup**: Checks for the .env file with API keys and offers to create it if missing
5. **Error Handling**: Provides clear error messages and recovery options

## Available Scripts

### 1. `trading_bot_manager.bat` (Recommended)

This is an all-in-one management script with a user-friendly menu interface.

**Features:**
- Start the bot with default investment (30 USDT)
- Start the bot with custom investment amount
- Open web monitor in browser
- Stop all services
- View logs
- Check performance
- Display current status of services

**Usage:**
```
trading_bot_manager.bat
```

### 2. `start.bat`

Simple script to start the trading bot and web monitor with default settings.

**Features:**
- Starts the trading bot in a new window
- Starts the web monitor in a new window
- Opens the web interface in your browser

**Usage:**
```
start.bat
```

### 3. `start_with_input.bat`

Enhanced start script that allows you to specify the investment amount.

**Features:**
- Prompts for investment amount
- Starts the trading bot with the specified amount
- Starts the web monitor in a new window
- Opens the web interface in your browser

**Usage:**
```
start_with_input.bat
```

### 4. `stop.bat`

Script to stop all trading bot services.

**Features:**
- Stops the trading bot
- Stops the web monitor
- Cleans up temporary files

**Usage:**
```
stop.bat
```

## Troubleshooting

### If the scripts don't work:

1. **Python Path Issues**
   - Make sure Python is installed and added to your PATH
   - Try running `python --version` in a command prompt to verify

2. **Permission Issues**
   - Try running the scripts as administrator
   - Right-click on the batch file and select "Run as administrator"

3. **Port Conflicts**
   - If the web monitor fails to start, port 5000 might be in use
   - Check if other applications are using port 5000
   - You can modify the port in `trade_monitor.py` if needed

4. **Process Termination Issues**
   - If the stop script doesn't work, manually close the command windows
   - Use Task Manager to end any remaining Python processes

## Best Practices

1. **Always use the stop script** before closing your computer to ensure all processes are properly terminated.

2. **Check the logs regularly** to monitor the bot's performance and detect any issues.

3. **Use the trading_bot_manager.bat** for the most user-friendly experience.

4. **Back up your trade_history.json file** periodically to keep a record of your trading history.
