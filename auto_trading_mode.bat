@echo off
setlocal enabledelayedexpansion
mode con: cols=100 lines=30
color 0A

echo ========================================================================
echo                  TRADING BOT AUTO MODE CONFIGURATION
echo ========================================================================
echo.

:: Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

:: Create config directories if they don't exist
if not exist config mkdir config
if not exist config\auto_mode mkdir config\auto_mode

:: Check if auto mode configuration exists
if not exist config\auto_mode\schedule.json (
    echo No auto mode configuration found. Creating default configuration...

    echo {> config\auto_mode\schedule.json
    echo   "enabled": false,>> config\auto_mode\schedule.json
    echo   "investment_amount": 30.0,>> config\auto_mode\schedule.json
    echo   "paper_trading": true,>> config\auto_mode\schedule.json
    echo   "paper_balance": 1000.0,>> config\auto_mode\schedule.json
    echo   "start_time": "09:00",>> config\auto_mode\schedule.json
    echo   "end_time": "17:00",>> config\auto_mode\schedule.json
    echo   "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],>> config\auto_mode\schedule.json
    echo   "max_runtime_hours": 8,>> config\auto_mode\schedule.json
    echo   "auto_restart": true,>> config\auto_mode\schedule.json
    echo   "restart_interval_hours": 24>> config\auto_mode\schedule.json
    echo }>> config\auto_mode\schedule.json
)

:: Create default configuration if file doesn't exist or is invalid
python read_config.py

:MENU
cls
echo ========================================================================
echo                  TRADING BOT AUTO MODE CONFIGURATION
echo ========================================================================
echo.

:: Read current configuration using a more robust method
python read_config.py

:: Load values from temp file with error handling
if exist config\auto_mode\temp.txt (
    for /f "usebackq delims== tokens=1,2" %%a in ("config\auto_mode\temp.txt") do (
        set "%%a=%%b"
    )
    del config\auto_mode\temp.txt
) else (
    echo Could not find config\auto_mode\temp.txt
    :: Set default values
    set ENABLED=false
    set AMOUNT=30.0
    set PAPER_TRADING=true
    set PAPER_BALANCE=1000.0
    set START_TIME=09:00
    set END_TIME=17:00
    set DAYS=Monday,Tuesday,Wednesday,Thursday,Friday
    set MAX_RUNTIME=8
    set AUTO_RESTART=true
    set RESTART_INTERVAL=24
)

:: Display current configuration
echo Current Auto Mode Configuration:
echo.
if "%ENABLED%" == "true" (
    echo Status: ENABLED
) else (
    echo Status: DISABLED
)
if "%PAPER_TRADING%" == "true" (
    echo Trading Mode: PAPER TRADING ^(Virtual Money^)
    echo Virtual Balance: %PAPER_BALANCE% USDT
) else (
    echo Trading Mode: LIVE TRADING ^(Real Money^)
)
echo Investment Amount: %AMOUNT% USDT
echo Start Time: %START_TIME%
echo End Time: %END_TIME%
echo Trading Days: %DAYS%
echo Maximum Runtime: %MAX_RUNTIME% hours
if "%AUTO_RESTART%" == "true" (
    echo Auto Restart: ENABLED ^(Every %RESTART_INTERVAL% hours^)
) else (
    echo Auto Restart: DISABLED
)
echo.
echo ========================================================================
echo.
echo  [1] Enable/Disable Auto Mode
echo  [2] Switch Trading Mode (Paper/Live)
echo  [3] Set Investment Amount
echo  [4] Set Paper Trading Balance
echo  [5] Set Start Time
echo  [6] Set End Time
echo  [7] Configure Trading Days
echo  [8] Set Maximum Runtime
echo  [9] Enable/Disable Auto Restart
echo  [A] Set Restart Interval
echo  [B] Start Auto Mode Now
echo  [0] Return to Main Menu
echo.
echo ========================================================================
echo.

set /p CHOICE="Enter your choice (0-B): "

if "%CHOICE%"=="0" goto EXIT
if "%CHOICE%"=="1" goto TOGGLE_ENABLED
if "%CHOICE%"=="2" goto TOGGLE_PAPER_TRADING
if "%CHOICE%"=="3" goto SET_AMOUNT
if "%CHOICE%"=="4" goto SET_PAPER_BALANCE
if "%CHOICE%"=="5" goto SET_START_TIME
if "%CHOICE%"=="6" goto SET_END_TIME
if "%CHOICE%"=="7" goto SET_DAYS
if "%CHOICE%"=="8" goto SET_MAX_RUNTIME
if "%CHOICE%"=="9" goto TOGGLE_AUTO_RESTART
if "%CHOICE%"=="A" goto SET_RESTART_INTERVAL
if "%CHOICE%"=="B" goto START_AUTO_MODE

echo Invalid choice. Please try again.
timeout /t 2 > nul
goto MENU

:TOGGLE_ENABLED
if "%ENABLED%" == "true" (
    set NEW_ENABLED=false
    echo Disabling Auto Mode...
) else (
    set NEW_ENABLED=true
    echo Enabling Auto Mode...
)

python toggle_enabled.py %NEW_ENABLED%
timeout /t 1 > nul
goto MENU

:TOGGLE_PAPER_TRADING
if "%PAPER_TRADING%" == "true" (
    set NEW_PAPER_TRADING=false
    echo Switching to LIVE TRADING...
) else (
    set NEW_PAPER_TRADING=true
    echo Switching to PAPER TRADING...
)

python toggle_paper_trading.py %NEW_PAPER_TRADING%
timeout /t 1 > nul
goto MENU

:SET_AMOUNT
echo.
set /p NEW_AMOUNT="Enter investment amount in USDT: "

python set_amount.py %NEW_AMOUNT%
timeout /t 2 > nul
goto MENU

:SET_PAPER_BALANCE
echo.
set /p NEW_PAPER_BALANCE="Enter paper trading balance in USDT: "

python set_paper_balance.py %NEW_PAPER_BALANCE%
timeout /t 2 > nul
goto MENU

:SET_START_TIME
echo.
echo Enter start time in 24-hour format (HH:MM)
set /p NEW_START_TIME="Start time: "

python set_start_time.py %NEW_START_TIME%
timeout /t 2 > nul
goto MENU

:SET_END_TIME
echo.
echo Enter end time in 24-hour format (HH:MM)
set /p NEW_END_TIME="End time: "

python set_end_time.py %NEW_END_TIME%
timeout /t 2 > nul
goto MENU

:SET_DAYS
cls
echo ========================================================================
echo                  CONFIGURE TRADING DAYS
echo ========================================================================
echo.
echo Current trading days: %DAYS%
echo.
echo Select trading days:
echo  [1] Monday
echo  [2] Tuesday
echo  [3] Wednesday
echo  [4] Thursday
echo  [5] Friday
echo  [6] Saturday
echo  [7] Sunday
echo  [8] Weekdays only (Mon-Fri)
echo  [9] All days
echo  [0] Return to previous menu
echo.
echo Enter the numbers for the days you want to enable, separated by spaces.
echo Example: 1 3 5 (for Monday, Wednesday, Friday)
echo.
set /p DAY_CHOICE="Your selection: "

if "%DAY_CHOICE%"=="0" goto MENU
if "%DAY_CHOICE%"=="8" set DAY_CHOICE=1 2 3 4 5
if "%DAY_CHOICE%"=="9" set DAY_CHOICE=1 2 3 4 5 6 7

set DAYS_JSON="["
set ADD_COMMA=0

if not "x%DAY_CHOICE:1=%"=="x%DAY_CHOICE%" (
    set DAYS_JSON=%DAYS_JSON%"Monday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:2=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Tuesday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:3=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Wednesday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:4=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Thursday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:5=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Friday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:6=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Saturday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:7=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Sunday"
)

set DAYS_JSON=%DAYS_JSON%]

python set_days.py "%DAYS_JSON%"
timeout /t 2 > nul
goto MENU

:SET_MAX_RUNTIME
echo.
set /p NEW_RUNTIME="Enter maximum runtime in hours: "

python set_max_runtime.py %NEW_RUNTIME%
timeout /t 2 > nul
goto MENU

:TOGGLE_AUTO_RESTART
if "%AUTO_RESTART%" == "true" (
    set NEW_AUTO_RESTART=false
    echo Disabling Auto Restart...
) else (
    set NEW_AUTO_RESTART=true
    echo Enabling Auto Restart...
)

python toggle_auto_restart.py %NEW_AUTO_RESTART%
timeout /t 1 > nul
goto MENU

:SET_RESTART_INTERVAL
echo.
set /p NEW_INTERVAL="Enter restart interval in hours: "

python set_restart_interval.py %NEW_INTERVAL%
timeout /t 2 > nul
goto MENU

:START_AUTO_MODE
cls
echo ========================================================================
echo                  STARTING TRADING BOT IN AUTO MODE
echo ========================================================================
echo.
echo The trading bot will now run in auto mode with the following settings:
echo.
echo Investment Amount: %AMOUNT% USDT
echo Start Time: %START_TIME%
echo End Time: %END_TIME%
echo Trading Days: %DAYS%
echo Maximum Runtime: %MAX_RUNTIME% hours
if "%AUTO_RESTART%" == "true" (
    echo Auto Restart: ENABLED ^(Every %RESTART_INTERVAL% hours^)
) else (
    echo Auto Restart: DISABLED
)
echo.
echo The bot will automatically start and stop according to this schedule.
echo.

:: Enable auto mode first
echo Enabling auto mode...
python toggle_enabled.py true

echo Starting web monitor...
start "Web Monitor" python -c "from web.monitor import create_templates, start_web_monitor; create_templates(); start_web_monitor('0.0.0.0', 5000)"

echo Waiting for web monitor to start...
timeout /t 3 /nobreak >nul

echo Opening web interface...
start http://localhost:5000

echo.
echo Starting trading bot directly...
start "Trading Bot" python main.py --amount %AMOUNT%

echo.
echo ========================================
echo  TRADING BOT SYSTEM STARTED
echo ========================================
echo.
echo Web Monitor: http://localhost:5000
echo Trading Bot: Running in separate window
echo.
echo The trading bot is now running with your settings.
echo Monitor your trades at: http://localhost:5000
echo.
echo Press any key to return to the menu...
pause > nul
goto MENU

:EXIT
echo Returning to main menu...
timeout /t 1 > nul
exit /b 0

endlocal
