@echo off
setlocal enabledelayedexpansion
mode con: cols=100 lines=30
color 0A

echo ========================================================================
echo                  TRADING BOT AUTO MODE CONFIGURATION
echo ========================================================================
echo.

:: Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

:: Create config directories if they don't exist
if not exist config mkdir config
if not exist config\auto_mode mkdir config\auto_mode

:: Check if auto mode configuration exists
if not exist config\auto_mode\schedule.json (
    echo No auto mode configuration found. Creating default configuration...

    echo {> config\auto_mode\schedule.json
    echo   "enabled": false,>> config\auto_mode\schedule.json
    echo   "investment_amount": 30.0,>> config\auto_mode\schedule.json
    echo   "paper_trading": true,>> config\auto_mode\schedule.json
    echo   "paper_balance": 1000.0,>> config\auto_mode\schedule.json
    echo   "start_time": "09:00",>> config\auto_mode\schedule.json
    echo   "end_time": "17:00",>> config\auto_mode\schedule.json
    echo   "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],>> config\auto_mode\schedule.json
    echo   "max_runtime_hours": 8,>> config\auto_mode\schedule.json
    echo   "auto_restart": true,>> config\auto_mode\schedule.json
    echo   "restart_interval_hours": 24>> config\auto_mode\schedule.json
    echo }>> config\auto_mode\schedule.json
)

:: Create default configuration if file doesn't exist or is invalid
python read_config.py

:MENU
cls
echo ========================================================================
echo                  TRADING BOT AUTO MODE CONFIGURATION
echo ========================================================================
echo.

:: Read current configuration using a more robust method
python read_config.py

:: Load values from temp file
for /f "tokens=*" %%a in (config\auto_mode\temp.txt) do set %%a
del config\auto_mode\temp.txt

:: Display current configuration
echo Current Auto Mode Configuration:
echo.
if "%ENABLED%" == "true" (
    echo Status: ENABLED
) else (
    echo Status: DISABLED
)
if "%PAPER_TRADING%" == "true" (
    echo Trading Mode: PAPER TRADING ^(Virtual Money^)
    echo Virtual Balance: %PAPER_BALANCE% USDT
) else (
    echo Trading Mode: LIVE TRADING ^(Real Money^)
)
echo Investment Amount: %AMOUNT% USDT
echo Start Time: %START_TIME%
echo End Time: %END_TIME%
echo Trading Days: %DAYS%
echo Maximum Runtime: %MAX_RUNTIME% hours
if "%AUTO_RESTART%" == "true" (
    echo Auto Restart: ENABLED ^(Every %RESTART_INTERVAL% hours^)
) else (
    echo Auto Restart: DISABLED
)
echo.
echo ========================================================================
echo.
echo  [1] Enable/Disable Auto Mode
echo  [2] Switch Trading Mode (Paper/Live)
echo  [3] Set Investment Amount
echo  [4] Set Paper Trading Balance
echo  [5] Set Start Time
echo  [6] Set End Time
echo  [7] Configure Trading Days
echo  [8] Set Maximum Runtime
echo  [9] Enable/Disable Auto Restart
echo  [A] Set Restart Interval
echo  [B] Start Auto Mode Now
echo  [0] Return to Main Menu
echo.
echo ========================================================================
echo.

set /p CHOICE="Enter your choice (0-B): "

if "%CHOICE%"=="0" goto EXIT
if "%CHOICE%"=="1" goto TOGGLE_ENABLED
if "%CHOICE%"=="2" goto TOGGLE_PAPER_TRADING
if "%CHOICE%"=="3" goto SET_AMOUNT
if "%CHOICE%"=="4" goto SET_PAPER_BALANCE
if "%CHOICE%"=="5" goto SET_START_TIME
if "%CHOICE%"=="6" goto SET_END_TIME
if "%CHOICE%"=="7" goto SET_DAYS
if "%CHOICE%"=="8" goto SET_MAX_RUNTIME
if "%CHOICE%"=="9" goto TOGGLE_AUTO_RESTART
if "%CHOICE%"=="A" goto SET_RESTART_INTERVAL
if "%CHOICE%"=="B" goto START_AUTO_MODE

echo Invalid choice. Please try again.
timeout /t 2 > nul
goto MENU

:TOGGLE_ENABLED
if "%ENABLED%" == "true" (
    set NEW_ENABLED=false
    echo Disabling Auto Mode...
) else (
    set NEW_ENABLED=true
    echo Enabling Auto Mode...
)

python toggle_enabled.py %NEW_ENABLED%
timeout /t 1 > nul
goto MENU

:TOGGLE_PAPER_TRADING
if "%PAPER_TRADING%" == "true" (
    set NEW_PAPER_TRADING=false
    echo Switching to LIVE TRADING...
) else (
    set NEW_PAPER_TRADING=true
    echo Switching to PAPER TRADING...
)

python toggle_paper_trading.py %NEW_PAPER_TRADING%
timeout /t 1 > nul
goto MENU

:SET_AMOUNT
echo.
set /p NEW_AMOUNT="Enter investment amount in USDT: "

python set_amount.py %NEW_AMOUNT%
timeout /t 2 > nul
goto MENU

:SET_PAPER_BALANCE
echo.
set /p NEW_PAPER_BALANCE="Enter paper trading balance in USDT: "

python set_paper_balance.py %NEW_PAPER_BALANCE%
timeout /t 2 > nul
goto MENU

:SET_START_TIME
echo.
echo Enter start time in 24-hour format (HH:MM)
set /p NEW_START_TIME="Start time: "

python set_start_time.py %NEW_START_TIME%
timeout /t 2 > nul
goto MENU

:SET_END_TIME
echo.
echo Enter end time in 24-hour format (HH:MM)
set /p NEW_END_TIME="End time: "

python set_end_time.py %NEW_END_TIME%
timeout /t 2 > nul
goto MENU

:SET_DAYS
cls
echo ========================================================================
echo                  CONFIGURE TRADING DAYS
echo ========================================================================
echo.
echo Current trading days: %DAYS%
echo.
echo Select trading days:
echo  [1] Monday
echo  [2] Tuesday
echo  [3] Wednesday
echo  [4] Thursday
echo  [5] Friday
echo  [6] Saturday
echo  [7] Sunday
echo  [8] Weekdays only (Mon-Fri)
echo  [9] All days
echo  [0] Return to previous menu
echo.
echo Enter the numbers for the days you want to enable, separated by spaces.
echo Example: 1 3 5 (for Monday, Wednesday, Friday)
echo.
set /p DAY_CHOICE="Your selection: "

if "%DAY_CHOICE%"=="0" goto MENU
if "%DAY_CHOICE%"=="8" set DAY_CHOICE=1 2 3 4 5
if "%DAY_CHOICE%"=="9" set DAY_CHOICE=1 2 3 4 5 6 7

set DAYS_JSON="["
set ADD_COMMA=0

if not "x%DAY_CHOICE:1=%"=="x%DAY_CHOICE%" (
    set DAYS_JSON=%DAYS_JSON%"Monday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:2=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Tuesday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:3=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Wednesday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:4=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Thursday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:5=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Friday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:6=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Saturday"
    set ADD_COMMA=1
)
if not "x%DAY_CHOICE:7=%"=="x%DAY_CHOICE%" (
    if %ADD_COMMA%==1 set DAYS_JSON=%DAYS_JSON%,
    set DAYS_JSON=%DAYS_JSON%"Sunday"
)

set DAYS_JSON=%DAYS_JSON%]

python set_days.py "%DAYS_JSON%"
timeout /t 2 > nul
goto MENU

:SET_MAX_RUNTIME
echo.
set /p NEW_RUNTIME="Enter maximum runtime in hours: "

python set_max_runtime.py %NEW_RUNTIME%
timeout /t 2 > nul
goto MENU

:TOGGLE_AUTO_RESTART
if "%AUTO_RESTART%" == "true" (
    set NEW_AUTO_RESTART=false
    echo Disabling Auto Restart...
) else (
    set NEW_AUTO_RESTART=true
    echo Enabling Auto Restart...
)

python toggle_auto_restart.py %NEW_AUTO_RESTART%
timeout /t 1 > nul
goto MENU

:SET_RESTART_INTERVAL
echo.
set /p NEW_INTERVAL="Enter restart interval in hours: "

python set_restart_interval.py %NEW_INTERVAL%
timeout /t 2 > nul
goto MENU

:START_AUTO_MODE
cls
echo ========================================================================
echo                  STARTING TRADING BOT IN AUTO MODE
echo ========================================================================
echo.
echo The trading bot will now run in auto mode with the following settings:
echo.
echo Investment Amount: %AMOUNT% USDT
echo Start Time: %START_TIME%
echo End Time: %END_TIME%
echo Trading Days: %DAYS%
echo Maximum Runtime: %MAX_RUNTIME% hours
if "%AUTO_RESTART%" == "true" (
    echo Auto Restart: ENABLED ^(Every %RESTART_INTERVAL% hours^)
) else (
    echo Auto Restart: DISABLED
)
echo.
echo The bot will automatically start and stop according to this schedule.
echo.
echo Creating auto mode runner script...

:: Create Python script to handle auto mode
echo import os, sys, time, json, datetime, subprocess, signal> auto_mode_runner.py
echo from datetime import datetime, timedelta>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def load_config():>> auto_mode_runner.py
echo     """Load configuration from file with error handling""">> auto_mode_runner.py
echo     default_config = {>> auto_mode_runner.py
echo         'enabled': False,>> auto_mode_runner.py
echo         'investment_amount': 30.0,>> auto_mode_runner.py
echo         'start_time': '09:00',>> auto_mode_runner.py
echo         'end_time': '17:00',>> auto_mode_runner.py
echo         'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],>> auto_mode_runner.py
echo         'max_runtime_hours': 8,>> auto_mode_runner.py
echo         'auto_restart': True,>> auto_mode_runner.py
echo         'restart_interval_hours': 24>> auto_mode_runner.py
echo     }>> auto_mode_runner.py
echo     try:>> auto_mode_runner.py
echo         if os.path.exists('config/auto_mode/schedule.json'):>> auto_mode_runner.py
echo             with open('config/auto_mode/schedule.json', 'r') as f:>> auto_mode_runner.py
echo                 content = f.read().strip()>> auto_mode_runner.py
echo                 if content:>> auto_mode_runner.py
echo                     config = json.loads(content)>> auto_mode_runner.py
echo                     # Ensure all required fields exist>> auto_mode_runner.py
echo                     for key, value in default_config.items():>> auto_mode_runner.py
echo                         if key not in config:>> auto_mode_runner.py
echo                             config[key] = value>> auto_mode_runner.py
echo                     return config>> auto_mode_runner.py
echo         # If we get here, either the file doesn't exist or is empty/invalid>> auto_mode_runner.py
echo         print("Using default configuration")>> auto_mode_runner.py
echo         # Create the config directory if it doesn't exist>> auto_mode_runner.py
echo         os.makedirs('config/auto_mode', exist_ok=True)>> auto_mode_runner.py
echo         # Write the default config to file>> auto_mode_runner.py
echo         with open('config/auto_mode/schedule.json', 'w') as f:>> auto_mode_runner.py
echo             json.dump(default_config, f, indent=2)>> auto_mode_runner.py
echo         return default_config>> auto_mode_runner.py
echo     except Exception as e:>> auto_mode_runner.py
echo         print(f"Error loading configuration: {e}")>> auto_mode_runner.py
echo         return default_config>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def is_trading_day(config):>> auto_mode_runner.py
echo     today = datetime.now().strftime('%%A')>> auto_mode_runner.py
echo     return today in config['days']>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def is_trading_time(config):>> auto_mode_runner.py
echo     now = datetime.now().time()>> auto_mode_runner.py
echo     start_time = datetime.strptime(config['start_time'], '%%H:%%M').time()>> auto_mode_runner.py
echo     end_time = datetime.strptime(config['end_time'], '%%H:%%M').time()>> auto_mode_runner.py
echo     return start_time <= now <= end_time>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def start_bot(config):>> auto_mode_runner.py
echo     print(f"Starting trading bot with {config['investment_amount']} USDT...")>> auto_mode_runner.py
echo     try:>> auto_mode_runner.py
echo         # Ensure config directory exists>> auto_mode_runner.py
echo         os.makedirs('config', exist_ok=True)>> auto_mode_runner.py
echo         os.makedirs('config/auto_mode', exist_ok=True)>> auto_mode_runner.py
echo         os.makedirs('data', exist_ok=True)>> auto_mode_runner.py
echo         os.makedirs('logs', exist_ok=True)>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo         # Kill any existing bot processes>> auto_mode_runner.py
echo         os.system('taskkill /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1')>> auto_mode_runner.py
echo         os.system('taskkill /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1')>> auto_mode_runner.py
echo         time.sleep(2)  # Wait for processes to terminate>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo         # Start the trading bot>> auto_mode_runner.py
echo         bot_process = subprocess.Popen(['python', 'main.py', '--amount', str(config['investment_amount'])])>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo         # Start the web monitor>> auto_mode_runner.py
echo         monitor_process = subprocess.Popen(['python', '-c', 'from web.monitor import start_web_monitor; start_web_monitor()'])>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo         print("Trading bot and web monitor started successfully.")>> auto_mode_runner.py
echo         return bot_process, monitor_process>> auto_mode_runner.py
echo     except Exception as e:>> auto_mode_runner.py
echo         print(f"Error starting bot: {e}")>> auto_mode_runner.py
echo         return None, None>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def stop_bot():>> auto_mode_runner.py
echo     print("Stopping trading bot...")>> auto_mode_runner.py
echo     os.system('taskkill /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1')>> auto_mode_runner.py
echo     os.system('taskkill /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1')>> auto_mode_runner.py
echo     os.system('taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Trading Bot*" /F > nul 2>&1')>> auto_mode_runner.py
echo     os.system('taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1')>> auto_mode_runner.py
echo     print("Trading bot stopped.")>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo def main():>> auto_mode_runner.py
echo     print("Starting Auto Mode Runner...")>> auto_mode_runner.py
echo     bot_process = None>> auto_mode_runner.py
echo     monitor_process = None>> auto_mode_runner.py
echo     last_restart = datetime.now()>> auto_mode_runner.py
echo     start_time = datetime.now()>> auto_mode_runner.py
echo     error_count = 0  # Track consecutive errors>> auto_mode_runner.py
echo     max_errors = 5   # Maximum consecutive errors before exiting>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo     try:>> auto_mode_runner.py
echo         while True:>> auto_mode_runner.py
echo             try:>> auto_mode_runner.py
echo                 config = load_config()>> auto_mode_runner.py
echo                 # Reset error count on successful config load>> auto_mode_runner.py
echo                 error_count = 0>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo                 if not config['enabled']:>> auto_mode_runner.py
echo                     print("Auto mode is disabled. Exiting...")>> auto_mode_runner.py
echo                     stop_bot()>> auto_mode_runner.py
echo                     break>> auto_mode_runner.py
echo                 now = datetime.now()>> auto_mode_runner.py
echo                 is_trading_day_now = is_trading_day(config)>> auto_mode_runner.py
echo                 is_trading_time_now = is_trading_time(config)>> auto_mode_runner.py
echo                 # Check if we need to restart the bot>> auto_mode_runner.py
echo                 if config['auto_restart'] and bot_process is not None:>> auto_mode_runner.py
echo                     restart_interval = timedelta(hours=config['restart_interval_hours'])>> auto_mode_runner.py
echo                     if now - last_restart > restart_interval:>> auto_mode_runner.py
echo                         print(f"Restart interval reached ({config['restart_interval_hours']} hours). Restarting bot...")>> auto_mode_runner.py
echo                         stop_bot()>> auto_mode_runner.py
echo                         time.sleep(5)  # Wait for processes to terminate>> auto_mode_runner.py
echo                         bot_process, monitor_process = start_bot(config)>> auto_mode_runner.py
echo                         last_restart = now>> auto_mode_runner.py
echo                         start_time = now>> auto_mode_runner.py
echo                 # Check if maximum runtime has been reached>> auto_mode_runner.py
echo                 max_runtime = timedelta(hours=config['max_runtime_hours'])>> auto_mode_runner.py
echo                 if bot_process is not None and now - start_time > max_runtime:>> auto_mode_runner.py
echo                     print(f"Maximum runtime reached ({config['max_runtime_hours']} hours). Stopping bot...")>> auto_mode_runner.py
echo                     stop_bot()>> auto_mode_runner.py
echo                     bot_process = None>> auto_mode_runner.py
echo                     monitor_process = None>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo                 # Start bot if it's trading time and not already running>> auto_mode_runner.py
echo                 if is_trading_day_now and is_trading_time_now and bot_process is None:>> auto_mode_runner.py
echo                     print("It's trading time. Starting bot...")>> auto_mode_runner.py
echo                     bot_process, monitor_process = start_bot(config)>> auto_mode_runner.py
echo                     last_restart = now>> auto_mode_runner.py
echo                     start_time = now>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo                 # Stop bot if it's not trading time and it's running>> auto_mode_runner.py
echo                 elif (not is_trading_day_now or not is_trading_time_now) and bot_process is not None:>> auto_mode_runner.py
echo                     print("Trading time is over. Stopping bot...")>> auto_mode_runner.py
echo                     stop_bot()>> auto_mode_runner.py
echo                     bot_process = None>> auto_mode_runner.py
echo                     monitor_process = None>> auto_mode_runner.py
echo                 # Status update>> auto_mode_runner.py
echo                 if bot_process is not None:>> auto_mode_runner.py
echo                     runtime = now - start_time>> auto_mode_runner.py
echo                     hours, remainder = divmod(runtime.total_seconds(), 3600)>> auto_mode_runner.py
echo                     minutes, seconds = divmod(remainder, 60)>> auto_mode_runner.py
echo                     print(f"Bot running for {int(hours)}h {int(minutes)}m {int(seconds)}s")>> auto_mode_runner.py
echo                 else:>> auto_mode_runner.py
echo                     if is_trading_day_now:>> auto_mode_runner.py
echo                         start_time_str = config['start_time']>> auto_mode_runner.py
echo                         end_time_str = config['end_time']>> auto_mode_runner.py
echo                         now_str = now.strftime('%%H:%%M')>> auto_mode_runner.py
echo                         print(f"Bot not running. Current time: {now_str}, Trading hours: {start_time_str} - {end_time_str}")>> auto_mode_runner.py
echo                     else:>> auto_mode_runner.py
echo                         print(f"Bot not running. Today ({now.strftime('%%A')}) is not a trading day.")>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo                 # Sleep before next check>> auto_mode_runner.py
echo                 time.sleep(60)  # Check every minute>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo             except Exception as e:>> auto_mode_runner.py
echo                 error_count += 1>> auto_mode_runner.py
echo                 print(f"Error in auto mode loop: {e}")>> auto_mode_runner.py
echo                 if error_count >= max_errors:>> auto_mode_runner.py
echo                     print(f"Too many consecutive errors ({max_errors}). Exiting...")>> auto_mode_runner.py
echo                     stop_bot()>> auto_mode_runner.py
echo                     break>> auto_mode_runner.py
echo                 # Sleep before retry>> auto_mode_runner.py
echo                 time.sleep(60)>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo     except KeyboardInterrupt:>> auto_mode_runner.py
echo         print("Auto mode runner stopped by user.")>> auto_mode_runner.py
echo         stop_bot()>> auto_mode_runner.py
echo     except Exception as e:>> auto_mode_runner.py
echo         print(f"Error in auto mode runner: {e}")>> auto_mode_runner.py
echo         stop_bot()>> auto_mode_runner.py
echo.>> auto_mode_runner.py
echo if __name__ == "__main__":>> auto_mode_runner.py
echo     main()>> auto_mode_runner.py

echo.
echo Starting auto mode runner...
start "Trading Bot Auto Mode" cmd /c "python auto_mode_runner.py"

echo.
echo Auto mode runner started in a separate window.
echo The trading bot will now run according to your schedule.
echo.
echo Press any key to return to the menu...
pause > nul
goto MENU

:EXIT
echo Returning to main menu...
timeout /t 1 > nul
exit /b 0

endlocal
