# Trading Bot Final Structure

This document outlines the final structure of the Trading Bot project after reorganization and optimization.

## Directory Structure

```
Trading Bot/
├── config/               # Configuration files
│   ├── auto_mode/        # Auto mode configuration
│   │   └── schedule.json # Auto mode schedule settings
│   ├── config.json       # User configuration
│   └── settings.py       # Settings management
├── data/                 # Data storage
│   ├── trade_history.json    # Record of all trades
│   ├── selected_coins.json   # Currently selected coins
│   ├── performance_data.json # Performance metrics
│   ├── coin_analysis.json    # Coin analysis results
│   └── backups/          # Automatic backups
├── docs/                 # Documentation
│   ├── AUTO_MODE_GUIDE.md # Auto mode and web service guide
│   ├── BATCH_SCRIPTS.md  # Batch scripts documentation
│   ├── DOCUMENTATION.md  # General documentation
│   ├── FINAL_STRUCTURE.md # This file
│   ├── QUICK_START.md    # Quick start guide
│   ├── README.md         # Main documentation
│   └── USER_GUIDE.md     # User guide
├── logs/                 # Log files
│   └── trading_log.txt   # Detailed activity log
├── modules/              # Core functionality
│   └── coin_analyzer.py  # Coin selection and analysis
├── utils/                # Utility functions
│   ├── backup.py         # Backup management
│   └── notifications.py  # Email/Telegram notifications
├── web/                  # Web interface
│   ├── monitor.py        # Web server
│   ├── templates/        # HTML templates
│   │   └── monitor.html  # Dashboard template
│   └── static/           # Static assets
├── .env                  # Environment variables (API keys)
├── auto_mode_runner.py   # Auto mode runner script
├── auto_trading_mode.bat # Auto mode configuration script
├── main.py               # Main entry point
├── README.md             # Documentation (root copy)
├── requirements.txt      # Python dependencies
├── start_trading.bat     # Startup script
├── start_web_monitor.bat # Web monitor startup script
├── stop_trading.bat      # Shutdown script
├── stop_web_monitor.bat  # Web monitor shutdown script
└── trading_bot_manager.bat # Comprehensive management script
```

## Key Components

### 1. Core Modules

- **main.py**: The main entry point for the trading bot. It integrates all components and provides a unified interface.
- **modules/coin_analyzer.py**: Responsible for analyzing and selecting the most promising coins from Binance based on various metrics.

### 2. Configuration

- **config/config.json**: User-configurable settings for the trading bot.
- **config/settings.py**: Manages loading and saving configuration settings.

### 3. Utilities

- **utils/backup.py**: Handles automatic backups of important data files.
- **utils/notifications.py**: Provides email and Telegram notification capabilities.

### 4. Web Interface

- **web/monitor.py**: Implements a web-based monitoring interface.
- **web/templates/monitor.html**: HTML template for the dashboard.

### 5. Startup and Management Scripts

- **start_trading.bat**: One-click startup script that checks dependencies and starts the bot.
- **stop_trading.bat**: Script to safely stop the trading bot.
- **trading_bot_manager.bat**: Comprehensive management script with a menu interface.
- **auto_trading_mode.bat**: Script to configure and start the auto mode.
- **start_web_monitor.bat**: Script to start only the web monitor service.
- **stop_web_monitor.bat**: Script to stop the web monitor service.

### 6. Data Storage

- **data/**: Directory for storing all data files:
  - Trade history
  - Selected coins
  - Performance metrics
  - Backups

### 7. Documentation

- **docs/**: Directory containing all documentation files.
- **README.md**: Main documentation file (also available in the root directory).

## Execution Flow

### Manual Mode

1. User runs `start_trading.bat` or uses the Trading Bot Manager
2. Script checks for dependencies and installs if needed
3. User enters investment amount
4. Main bot process starts (`main.py`)
5. Web monitor starts in a separate thread
6. Bot analyzes and selects coins from Binance
7. Bot executes trades based on technical analysis
8. Performance data is tracked and displayed in the web interface
9. User can stop the bot using `stop_trading.bat`

### Auto Mode

1. User configures auto mode using `auto_trading_mode.bat` or from the Trading Bot Manager
2. User sets trading schedule (days, hours, investment amount)
3. Auto mode runner starts in a separate window
4. Bot automatically starts and stops according to the schedule
5. Bot restarts periodically if configured to do so
6. Performance data is tracked and displayed in the web interface
7. User can disable auto mode or modify the schedule at any time

### Web Monitor Only

1. User runs `start_web_monitor.bat`
2. Script checks for dependencies and installs if needed
3. User can configure host and port settings
4. Web monitor starts in a separate window
5. Web interface opens in the default browser
6. User can view trading data and performance metrics
7. User can stop the web monitor using `stop_web_monitor.bat`

## Dependencies

The trading bot requires the following Python packages:
- python-binance
- pandas
- ta (Technical Analysis library)
- python-dotenv
- flask

These dependencies are automatically checked and installed by the startup script.
