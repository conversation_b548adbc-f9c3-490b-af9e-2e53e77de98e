@echo off
echo ========================================================================
echo                  ENHANCED TRADING BOT SHUTDOWN SCRIPT
echo ========================================================================
echo.

:: Kill all cmd windows with "Enhanced Trading Bot" in the title
echo Stopping Trading Bot...
taskkill /FI "WINDOWTITLE eq Enhanced Trading Bot*" /F > nul 2>&1

:: Kill any remaining Python processes related to our scripts
echo Cleaning up any remaining processes...
taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Enhanced Trading Bot*" /F > nul 2>&1

echo.
echo ========================================================================
echo                 TRADING BOT STOPPED SUCCESSFULLY
echo ========================================================================
echo.
echo The trading bot has been stopped.
echo All trading activities have been terminated.
echo.
echo You can view the trading history and performance in the data directory:
echo  - Trade history: data/trade_history.json
echo  - Performance data: data/performance_data.json
echo  - Logs: logs/trading_log.txt
echo.
echo To restart the bot, run start_trading.bat
echo.
echo Press any key to exit...
pause > nul
