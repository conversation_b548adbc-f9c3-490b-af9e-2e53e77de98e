import os
import time
import math
import json
import pandas as pd
import datetime
import logging
from binance.client import Client
from binance.exceptions import BinanceAPIException
from ta.trend import EMAIndicator, MACD, SMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.volatility import BollingerBands
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_log.txt"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# Load API keys from .env
load_dotenv()
API_KEY = os.getenv("BINANCE_API_KEY")
API_SECRET = os.getenv("BINANCE_API_SECRET")

client = Client(API_KEY, API_SECRET)

# Token Pairs to Trade - Selected for higher volatility potential
PAIRS = [
    "INJUSDT",   # Injective - DeFi with high volatility
    "FETUSDT",   # Fetch.ai - AI token with growth potential
    "AGIXUSDT",  # SingularityNET - AI token with volatility
    "APTUSDT",   # Aptos - Layer 1 with strong momentum
    "SUIUSDT",   # Sui - New L1 blockchain with volatility
    "PEPEUSDT",  # Pepe - Meme coin with high volatility
    "FLOKIUSDT"  # Floki - Meme coin with high volatility
]

# Trade Settings - Optimized for higher returns
STOP_LOSS_PERCENT = 5.0        # Wider stop loss to avoid premature exits
TAKE_PROFIT_PERCENT = 15.0     # Target 15% minimum profit
TRAILING_STOP_PERCENT = 3.0    # Trailing stop to capture upside
MAX_TRADES_PER_DAY = 10        # Increased limit (Binance allows up to 1200 orders per minute)
INVESTMENT_PERIOD_DAYS = 7     # Track performance for 7 days

# Binance API Limits
# - Weight limit: 1200 weight per minute for API requests
# - Order rate: 50 orders per 10 seconds (5 orders/second)
# - Max open orders: 200 per trading pair, 500 total
API_COOLDOWN = 0.5             # Seconds between API calls to avoid rate limits

# Get trade amount with default of 30 USDT
default_amount = 30.0
try:
    user_input = input(f"Enter trade amount in USDT (default: {default_amount}): ")
    TRADE_AMOUNT = float(user_input) if user_input.strip() else default_amount
    logger.info(f"Using trade amount: {TRADE_AMOUNT} USDT")
except ValueError:
    logger.warning(f"Invalid input. Using default amount: {default_amount} USDT")
    TRADE_AMOUNT = default_amount

# Track performance
start_date = datetime.datetime.now()
end_date = start_date + datetime.timedelta(days=INVESTMENT_PERIOD_DAYS)
total_trades = 0
profitable_trades = 0
total_profit_loss = 0.0
trade_history = []  # Store trade history for analysis

# Initialize performance tracking
daily_profits = {}
pair_performance = {pair: {'trades': 0, 'wins': 0, 'profit_loss': 0.0} for pair in PAIRS}

logger.info(f"Starting trading bot with {TRADE_AMOUNT} USDT investment for {INVESTMENT_PERIOD_DAYS} days")
logger.info(f"Trading period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
logger.info(f"Monitoring pairs: {', '.join(PAIRS)}")
logger.info(f"Target profit: {TAKE_PROFIT_PERCENT}% per trade")

def get_klines(symbol, interval=Client.KLINE_INTERVAL_5MINUTE, limit=100):
    """
    Get historical klines data with improved error handling and multiple timeframes
    """
    try:
        data = client.get_klines(symbol=symbol, interval=interval, limit=limit)
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'num_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'])

        # Convert columns to numeric
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col])

        # Add timestamp in readable format
        df['date'] = pd.to_datetime(df['timestamp'], unit='ms')

        return df
    except BinanceAPIException as e:
        logger.error(f"[{symbol}] API error: {e}")
        return None
    except Exception as e:
        logger.error(f"[{symbol}] Unexpected error: {e}")
        return None

def calculate_indicators(df):
    """
    Calculate multiple technical indicators for better signal accuracy
    """
    close = df['close']
    high = df['high']
    low = df['low']
    volume = df['volume']

    # Trend indicators
    df['ema20'] = EMAIndicator(close, window=20).ema_indicator()
    df['ema50'] = EMAIndicator(close, window=50).ema_indicator()
    df['sma200'] = SMAIndicator(close, window=50).sma_indicator()  # Using 50 instead of 200 for shorter timeframe

    # Momentum indicators
    df['rsi'] = RSIIndicator(close, window=14).rsi()
    stoch = StochasticOscillator(high, low, close, window=14, smooth_window=3)
    df['stoch_k'] = stoch.stoch()
    df['stoch_d'] = stoch.stoch_signal()

    # MACD
    macd = MACD(close)
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()
    df['macd_diff'] = macd.macd_diff()

    # Volatility
    bb = BollingerBands(close, window=20, window_dev=2)
    df['bb_upper'] = bb.bollinger_hband()
    df['bb_lower'] = bb.bollinger_lband()
    df['bb_mid'] = bb.bollinger_mavg()
    df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_mid']

    # Volume analysis
    df['volume_ema'] = EMAIndicator(volume, window=20).ema_indicator()
    df['volume_ratio'] = volume / df['volume_ema']

    return df

def generate_signal(df):
    """
    Enhanced signal generation with multiple confirmation factors
    """
    # Calculate all indicators
    df = calculate_indicators(df)

    # Get latest values
    latest = df.iloc[-1]
    prev = df.iloc[-2]

    # Signal strength score (0-10)
    score = 0

    # Trend conditions
    if latest['close'] > latest['ema20']:
        score += 1
    if latest['ema20'] > latest['ema50']:
        score += 1
    if latest['close'] > prev['close']:
        score += 1

    # Momentum conditions
    if 30 <= latest['rsi'] <= 40:  # Oversold but starting to recover
        score += 2
    if latest['rsi'] > prev['rsi']:  # Rising RSI
        score += 1
    if latest['stoch_k'] < 30 and latest['stoch_k'] > latest['stoch_d']:  # Stochastic crossover from oversold
        score += 1

    # MACD conditions
    if latest['macd'] > latest['macd_signal'] and prev['macd'] <= prev['macd_signal']:  # Fresh MACD crossover
        score += 2
    if latest['macd_diff'] > 0 and latest['macd_diff'] > prev['macd_diff']:  # Increasing MACD histogram
        score += 1

    # Volatility conditions
    if latest['close'] < latest['bb_lower']:  # Price below lower Bollinger Band (potential bounce)
        score += 1
    if latest['bb_width'] > df['bb_width'].rolling(20).mean().iloc[-1]:  # Expanding volatility
        score += 1

    # Volume conditions
    if latest['volume_ratio'] > 1.5:  # Volume spike
        score += 1

    # Check if we have a strong enough signal (threshold of 7 out of 12 possible points)
    logger.info(f"Signal strength for {df['close'].name}: {score}/12")
    return score >= 7

def trade(symbol):
    """
    Enhanced trading function with trailing stop-loss and improved risk management
    """
    global total_trades, profitable_trades, total_profit_loss, pair_performance, daily_profits

    # Check if we've reached the end of our investment period
    if datetime.datetime.now() > end_date:
        logger.info(f"Investment period ended. Final results:")
        logger.info(f"Total trades: {total_trades}")
        if total_trades > 0:
            logger.info(f"Profitable trades: {profitable_trades} ({profitable_trades/total_trades*100:.1f}% win rate)")
            logger.info(f"Total P/L: {total_profit_loss:.2f} USDT ({total_profit_loss/TRADE_AMOUNT*100:.1f}%)")
        else:
            logger.info("No trades were executed during the investment period.")
        return

    # Check if we've exceeded our daily trade limit
    today = datetime.datetime.now().date()
    today_trades = sum(1 for t in trade_history if t['date'].date() == today)
    if today_trades >= MAX_TRADES_PER_DAY:
        logger.info(f"Daily trade limit reached ({MAX_TRADES_PER_DAY}). Waiting for next day.")
        return

    logger.info(f"[SCAN] Analyzing {symbol}...")

    # Get data from multiple timeframes for confirmation
    df_5m = get_klines(symbol, interval=Client.KLINE_INTERVAL_5MINUTE)
    df_15m = get_klines(symbol, interval=Client.KLINE_INTERVAL_15MINUTE)

    if df_5m is None or df_15m is None:
        logger.error(f"Could not retrieve data for {symbol}")
        return

    # Check for signals on both timeframes
    signal_5m = generate_signal(df_5m)
    signal_15m = generate_signal(df_15m)

    # Only proceed if we have signals on both timeframes
    if not (signal_5m and signal_15m):
        logger.info(f"[INFO] No strong entry signal for {symbol}")
        return

    # Get current price and calculate quantity
    try:
        ticker = client.get_symbol_ticker(symbol=symbol)
        price = float(ticker['price'])

        # Get symbol info for precision
        symbol_info = next((s for s in client.get_exchange_info()['symbols'] if s['symbol'] == symbol), None)
        if not symbol_info:
            logger.error(f"Could not get symbol info for {symbol}")
            return

        # Get the quantity precision
        lot_size_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE'), None)
        if not lot_size_filter:
            logger.error(f"Could not get LOT_SIZE filter for {symbol}")
            return

        # Calculate step size decimal places
        step_size = float(lot_size_filter['stepSize'])
        precision = int(round(-math.log10(step_size)))

        # Calculate quantity with correct precision
        quantity = round(TRADE_AMOUNT / price, precision)

        # Check minimum notional value
        min_notional_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'MIN_NOTIONAL'), None)
        if min_notional_filter and price * quantity < float(min_notional_filter['minNotional']):
            logger.warning(f"Order value too small for {symbol}. Minimum: {min_notional_filter['minNotional']} USDT")
            return

        logger.info(f"[SIGNAL] Strong signal detected: {symbol} @ {price} | Qty: {quantity}")

        # Execute buy order
        order = client.order_market_buy(symbol=symbol, quantity=quantity)
        buy_price = float(order['fills'][0]['price'])
        buy_time = datetime.datetime.now()

        logger.info(f"[BUY] Order executed: {buy_price} | Time: {buy_time}")

        # Initialize stop-loss and take-profit levels
        initial_stop_loss = buy_price * (1 - STOP_LOSS_PERCENT / 100)
        take_profit = buy_price * (1 + TAKE_PROFIT_PERCENT / 100)

        # Initialize trailing stop variables
        trailing_stop = initial_stop_loss
        highest_price = buy_price

        # Update pair statistics
        pair_performance[symbol]['trades'] += 1

        while True:
            current_price = float(client.get_symbol_ticker(symbol=symbol)['price'])
            current_time = datetime.datetime.now()
            time_in_trade = (current_time - buy_time).total_seconds() / 60  # minutes

            # Update highest price and trailing stop if price moves up
            if current_price > highest_price:
                highest_price = current_price
                # Only update trailing stop if it would be higher than the current one
                new_trailing_stop = highest_price * (1 - TRAILING_STOP_PERCENT / 100)
                if new_trailing_stop > trailing_stop:
                    trailing_stop = new_trailing_stop
                    logger.info(f"[UPDATE] New high: {highest_price} | Updated trailing stop: {trailing_stop}")

            # Calculate current profit/loss percentage
            current_pnl_pct = (current_price - buy_price) / buy_price * 100

            # Check exit conditions
            if current_price <= trailing_stop:
                logger.info(f"[STOP] Trailing stop triggered at {current_price} ({current_pnl_pct:.2f}%)")
                sell_order = client.order_market_sell(symbol=symbol, quantity=quantity)
                sell_price = float(sell_order['fills'][0]['price'])
                profit_loss = (sell_price - buy_price) * quantity

                # Update statistics
                total_trades += 1
                if sell_price > buy_price:
                    profitable_trades += 1
                    pair_performance[symbol]['wins'] += 1
                total_profit_loss += profit_loss
                pair_performance[symbol]['profit_loss'] += profit_loss

                # Log trade results
                logger.info(f"[SELL] Order executed: {sell_price}")
                logger.info(f"Trade P/L: {profit_loss:.2f} USDT ({(sell_price-buy_price)/buy_price*100:.2f}%)")
                logger.info(f"Time in trade: {time_in_trade:.1f} minutes")

                # Add to trade history
                trade_record = {
                    'symbol': symbol,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'quantity': quantity,
                    'profit_loss': profit_loss,
                    'profit_loss_pct': (sell_price-buy_price)/buy_price*100,
                    'date': buy_time,
                    'duration_minutes': time_in_trade
                }
                trade_history.append(trade_record)

                # Save trade history to JSON file for web monitor
                save_trade_history()

                break

            elif current_price >= take_profit:
                logger.info(f"[PROFIT] Take profit hit at {current_price} ({current_pnl_pct:.2f}%)")
                sell_order = client.order_market_sell(symbol=symbol, quantity=quantity)
                sell_price = float(sell_order['fills'][0]['price'])
                profit_loss = (sell_price - buy_price) * quantity

                # Update statistics
                total_trades += 1
                profitable_trades += 1
                pair_performance[symbol]['wins'] += 1
                total_profit_loss += profit_loss
                pair_performance[symbol]['profit_loss'] += profit_loss

                # Log trade results
                logger.info(f"[SELL] Order executed: {sell_price}")
                logger.info(f"Trade P/L: {profit_loss:.2f} USDT ({(sell_price-buy_price)/buy_price*100:.2f}%)")
                logger.info(f"Time in trade: {time_in_trade:.1f} minutes")

                # Add to trade history
                trade_record = {
                    'symbol': symbol,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'quantity': quantity,
                    'profit_loss': profit_loss,
                    'profit_loss_pct': (sell_price-buy_price)/buy_price*100,
                    'date': buy_time,
                    'duration_minutes': time_in_trade
                }
                trade_history.append(trade_record)

                # Save trade history to JSON file for web monitor
                save_trade_history()

                break

            # Force exit if trade has been open for too long (8 hours)
            elif time_in_trade > 480:  # 8 hours in minutes
                logger.info(f"[TIME] Time-based exit after {time_in_trade:.1f} minutes")
                sell_order = client.order_market_sell(symbol=symbol, quantity=quantity)
                sell_price = float(sell_order['fills'][0]['price'])
                profit_loss = (sell_price - buy_price) * quantity

                # Update statistics
                total_trades += 1
                if sell_price > buy_price:
                    profitable_trades += 1
                    pair_performance[symbol]['wins'] += 1
                total_profit_loss += profit_loss
                pair_performance[symbol]['profit_loss'] += profit_loss

                # Log trade results
                logger.info(f"[SELL] Order executed: {sell_price}")
                logger.info(f"Trade P/L: {profit_loss:.2f} USDT ({(sell_price-buy_price)/buy_price*100:.2f}%)")
                logger.info(f"Time in trade: {time_in_trade:.1f} minutes")

                # Add to trade history
                trade_record = {
                    'symbol': symbol,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'quantity': quantity,
                    'profit_loss': profit_loss,
                    'profit_loss_pct': (sell_price-buy_price)/buy_price*100,
                    'date': buy_time,
                    'duration_minutes': time_in_trade
                }
                trade_history.append(trade_record)

                # Save trade history to JSON file for web monitor
                save_trade_history()

                break

            else:
                logger.info(f"[MONITOR] {symbol} | Current: {current_price:.8f} | " +
                          f"P/L: {current_pnl_pct:.2f}% | Trailing Stop: {trailing_stop:.8f}")

            # Sleep to avoid API rate limits
            time.sleep(30)  # Check every 30 seconds

    except BinanceAPIException as e:
        logger.error(f"[ERROR] Trade error: {e}")
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error: {e}")

# Function to save trade history to JSON file for web monitor
def save_trade_history():
    """
    Save trade history to a JSON file for the web monitor
    """
    try:
        # Convert datetime objects to strings for JSON serialization
        serializable_history = []
        for trade in trade_history:
            trade_copy = trade.copy()
            if isinstance(trade_copy['date'], datetime.datetime):
                trade_copy['date'] = trade_copy['date'].strftime('%Y-%m-%d %H:%M:%S')
            serializable_history.append(trade_copy)

        with open('trade_history.json', 'w') as f:
            json.dump(serializable_history, f, indent=2)

        logger.debug(f"Trade history saved to trade_history.json ({len(serializable_history)} trades)")
    except Exception as e:
        logger.error(f"Error saving trade history: {e}")

# Function to generate performance report
def generate_report():
    """
    Generate a performance report for the trading period
    """
    logger.info("\n===== PERFORMANCE REPORT =====")
    logger.info(f"Trading period: {start_date.strftime('%Y-%m-%d')} to {datetime.datetime.now().strftime('%Y-%m-%d')}")
    logger.info(f"Total trades: {total_trades}")

    if total_trades > 0:
        win_rate = profitable_trades / total_trades * 100
        logger.info(f"Win rate: {win_rate:.1f}% ({profitable_trades}/{total_trades})")
        logger.info(f"Total P/L: {total_profit_loss:.2f} USDT ({total_profit_loss/TRADE_AMOUNT*100:.1f}%)")

        # Calculate average profit per trade
        avg_profit = total_profit_loss / total_trades
        logger.info(f"Average P/L per trade: {avg_profit:.2f} USDT ({avg_profit/TRADE_AMOUNT*100:.1f}%)")

        # Best performing pair
        best_pair = max(pair_performance.items(), key=lambda x: x[1]['profit_loss'])
        logger.info(f"Best performing pair: {best_pair[0]} with {best_pair[1]['profit_loss']:.2f} USDT profit")

        # Calculate daily returns
        if trade_history:
            for trade in trade_history:
                # Handle both datetime objects and strings
                if isinstance(trade['date'], datetime.datetime):
                    trade_date = trade['date'].date()
                else:
                    # If it's already a string, parse it
                    trade_date = datetime.datetime.strptime(trade['date'], '%Y-%m-%d %H:%M:%S').date()

                if trade_date not in daily_profits:
                    daily_profits[trade_date] = 0
                daily_profits[trade_date] += trade['profit_loss']

            # Average daily return
            avg_daily_return = sum(daily_profits.values()) / len(daily_profits)
            logger.info(f"Average daily return: {avg_daily_return:.2f} USDT ({avg_daily_return/TRADE_AMOUNT*100:.1f}%)")

            # Best day
            best_day = max(daily_profits.items(), key=lambda x: x[1])
            logger.info(f"Best day: {best_day[0]} with {best_day[1]:.2f} USDT profit")
    else:
        logger.info("No trades executed during the period.")

    logger.info("==============================\n")

# === MAIN LOOP ===
try:
    logger.info("Starting trading bot main loop...")

    while datetime.datetime.now() < end_date:
        # Check account balance to ensure we have enough funds
        account = client.get_account()
        usdt_balance = float(next((asset['free'] for asset in account['balances'] if asset['asset'] == 'USDT'), 0))

        if usdt_balance < TRADE_AMOUNT:
            logger.warning(f"Insufficient USDT balance: {usdt_balance}. Required: {TRADE_AMOUNT}")
            logger.info("Waiting for 30 minutes before checking again...")
            time.sleep(1800)  # Wait 30 minutes
            continue

        # Scan all pairs for trading opportunities
        for pair in PAIRS:
            # Update pair performance in case it's a new pair
            if pair not in pair_performance:
                pair_performance[pair] = {'trades': 0, 'wins': 0, 'profit_loss': 0.0}

            # Execute trade function
            trade(pair)

            # Sleep between pairs to avoid API rate limits
            time.sleep(15)

        # Generate daily report
        if datetime.datetime.now().hour == 0 and datetime.datetime.now().minute < 15:
            generate_report()

        logger.info("[CYCLE] Scan completed. Waiting before next scan...")
        time.sleep(300)  # Wait 5 minutes between full scans

    # Generate final report at the end of the investment period
    logger.info("Investment period completed!")
    generate_report()

except KeyboardInterrupt:
    logger.info("Bot stopped by user.")
    generate_report()
except Exception as e:
    logger.error(f"Critical error: {e}")
    generate_report()
