"""
Coin Analyzer Module

This module is responsible for analyzing and selecting the most promising coins
from Binance based on various metrics including:
- Price volatility
- Trading volume
- Recent price action
- Market trends
- Technical indicators

The module automatically updates the list of coins to trade based on real-time analysis.
"""

import os
import json
import time
import logging
import datetime
import pandas as pd
import numpy as np
from binance.client import Client
from binance.exceptions import BinanceAPIException
from ta.trend import EMAIndicator, MACD, SMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.volatility import BollingerBands, AverageTrueRange

# Configure logging
logger = logging.getLogger(__name__)

class CoinAnalyzer:
    """Class for analyzing and selecting coins based on market trends and technical analysis"""
    
    def __init__(self, client, config=None):
        """
        Initialize the CoinAnalyzer
        
        Args:
            client: Binance API client
            config: Configuration dictionary (optional)
        """
        self.client = client
        self.config = config or {}
        
        # Default configuration
        self.default_config = {
            'max_coins': 10,                  # Maximum number of coins to select
            'min_volume_usdt': 10000000,      # Minimum 24h volume in USDT
            'min_price_change': 3.0,          # Minimum 24h price change (%)
            'max_price_change': 50.0,         # Maximum 24h price change (%)
            'blacklist': ['BUSD', 'USDC', 'TUSD', 'DAI', 'USDP', 'USDT'],  # Excluded base currencies
            'update_interval': 3600,          # Seconds between updates (1 hour)
            'data_file': 'data/selected_coins.json',  # File to store selected coins
            'timeframes': ['1h', '4h', '1d'],  # Timeframes to analyze
            'volatility_weight': 0.3,         # Weight for volatility score
            'volume_weight': 0.2,             # Weight for volume score
            'trend_weight': 0.3,              # Weight for trend score
            'momentum_weight': 0.2,           # Weight for momentum score
        }
        
        # Merge default config with provided config
        for key, value in self.default_config.items():
            if key not in self.config:
                self.config[key] = value
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.config['data_file']), exist_ok=True)
        
        # Last update timestamp
        self.last_update = 0
        
        # Currently selected coins
        self.selected_coins = []
        
        # Load previously selected coins if available
        self._load_selected_coins()
    
    def _load_selected_coins(self):
        """Load previously selected coins from file"""
        try:
            if os.path.exists(self.config['data_file']):
                with open(self.config['data_file'], 'r') as f:
                    data = json.load(f)
                    self.selected_coins = data.get('coins', [])
                    self.last_update = data.get('timestamp', 0)
                    logger.info(f"Loaded {len(self.selected_coins)} coins from file")
        except Exception as e:
            logger.error(f"Error loading selected coins: {e}")
    
    def _save_selected_coins(self):
        """Save selected coins to file"""
        try:
            data = {
                'coins': self.selected_coins,
                'timestamp': self.last_update,
                'update_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.config['data_file'], 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved {len(self.selected_coins)} coins to file")
        except Exception as e:
            logger.error(f"Error saving selected coins: {e}")
    
    def get_top_coins(self, force_update=False):
        """
        Get the top coins based on analysis
        
        Args:
            force_update: Force update regardless of the update interval
            
        Returns:
            list: List of coin symbols (e.g., ["BTCUSDT", "ETHUSDT"])
        """
        current_time = time.time()
        
        # Check if update is needed
        if force_update or (current_time - self.last_update) > self.config['update_interval']:
            logger.info("Updating coin selection...")
            self._update_coin_selection()
            self.last_update = current_time
            self._save_selected_coins()
        
        return self.selected_coins
    
    def _update_coin_selection(self):
        """Update the list of selected coins based on market analysis"""
        try:
            # Get all USDT trading pairs
            exchange_info = self.client.get_exchange_info()
            all_symbols = [s['symbol'] for s in exchange_info['symbols'] 
                          if s['symbol'].endswith('USDT') and 
                          s['status'] == 'TRADING' and
                          not any(s['symbol'].startswith(b) for b in self.config['blacklist'])]
            
            logger.info(f"Found {len(all_symbols)} USDT trading pairs")
            
            # Get 24hr ticker for all symbols
            tickers_24hr = self.client.get_ticker()
            
            # Filter and score coins
            coin_scores = []
            
            for ticker in tickers_24hr:
                symbol = ticker['symbol']
                
                # Skip non-USDT pairs
                if not symbol.endswith('USDT'):
                    continue
                
                # Skip blacklisted coins
                if any(symbol.startswith(b) for b in self.config['blacklist']):
                    continue
                
                try:
                    # Extract metrics
                    volume_usdt = float(ticker['quoteVolume'])
                    price_change_pct = float(ticker['priceChangePercent'])
                    
                    # Apply filters
                    if (volume_usdt < self.config['min_volume_usdt'] or
                        abs(price_change_pct) < self.config['min_price_change'] or
                        abs(price_change_pct) > self.config['max_price_change']):
                        continue
                    
                    # Calculate initial score based on volume and price change
                    volume_score = min(volume_usdt / 100000000, 1.0)  # Normalize volume (max 100M)
                    volatility_score = min(abs(price_change_pct) / 20.0, 1.0)  # Normalize volatility (max 20%)
                    
                    # Get detailed analysis for promising coins
                    trend_score, momentum_score = self._analyze_coin(symbol)
                    
                    # Calculate final score
                    final_score = (
                        self.config['volatility_weight'] * volatility_score +
                        self.config['volume_weight'] * volume_score +
                        self.config['trend_weight'] * trend_score +
                        self.config['momentum_weight'] * momentum_score
                    )
                    
                    coin_scores.append({
                        'symbol': symbol,
                        'score': final_score,
                        'volume': volume_usdt,
                        'price_change': price_change_pct,
                        'trend_score': trend_score,
                        'momentum_score': momentum_score
                    })
                    
                except Exception as e:
                    logger.warning(f"Error analyzing {symbol}: {e}")
            
            # Sort by score and select top coins
            coin_scores.sort(key=lambda x: x['score'], reverse=True)
            top_coins = coin_scores[:self.config['max_coins']]
            
            # Update selected coins
            self.selected_coins = [coin['symbol'] for coin in top_coins]
            
            logger.info(f"Selected {len(self.selected_coins)} coins: {', '.join(self.selected_coins)}")
            
            # Save detailed analysis
            self._save_analysis_details(coin_scores)
            
        except Exception as e:
            logger.error(f"Error updating coin selection: {e}")
            # Keep using previous selection if there's an error
    
    def _analyze_coin(self, symbol):
        """
        Perform detailed technical analysis on a coin
        
        Args:
            symbol: Coin symbol (e.g., "BTCUSDT")
            
        Returns:
            tuple: (trend_score, momentum_score)
        """
        try:
            # Initialize scores
            trend_score = 0.0
            momentum_score = 0.0
            
            # Analyze multiple timeframes
            for interval in self.config['timeframes']:
                # Convert interval to Binance format
                binance_interval = self._convert_interval(interval)
                
                # Get klines data
                klines = self.client.get_klines(symbol=symbol, interval=binance_interval, limit=100)
                
                # Convert to dataframe
                df = pd.DataFrame(klines, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_volume', 'trades_count',
                    'taker_buy_base', 'taker_buy_quote', 'ignored'
                ])
                
                # Convert to numeric
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = pd.to_numeric(df[col])
                
                # Calculate indicators
                df = self._calculate_indicators(df)
                
                # Calculate scores for this timeframe
                timeframe_trend_score = self._calculate_trend_score(df)
                timeframe_momentum_score = self._calculate_momentum_score(df)
                
                # Weight longer timeframes more heavily
                if interval == '1h':
                    weight = 0.2
                elif interval == '4h':
                    weight = 0.3
                else:  # 1d
                    weight = 0.5
                
                trend_score += timeframe_trend_score * weight
                momentum_score += timeframe_momentum_score * weight
            
            return trend_score, momentum_score
            
        except Exception as e:
            logger.warning(f"Error in detailed analysis for {symbol}: {e}")
            return 0.0, 0.0
    
    def _convert_interval(self, interval):
        """Convert interval to Binance format"""
        if interval == '1h':
            return Client.KLINE_INTERVAL_1HOUR
        elif interval == '4h':
            return Client.KLINE_INTERVAL_4HOUR
        elif interval == '1d':
            return Client.KLINE_INTERVAL_1DAY
        else:
            return Client.KLINE_INTERVAL_1HOUR
    
    def _calculate_indicators(self, df):
        """Calculate technical indicators for a dataframe"""
        # Trend indicators
        df['ema20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = EMAIndicator(close=df['close'], window=50).ema_indicator()
        df['sma200'] = SMAIndicator(close=df['close'], window=50).sma_indicator()
        
        # MACD
        macd = MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        df['macd_diff'] = macd.macd_diff()
        
        # RSI
        df['rsi'] = RSIIndicator(close=df['close']).rsi()
        
        # Bollinger Bands
        bollinger = BollingerBands(close=df['close'])
        df['bb_upper'] = bollinger.bollinger_hband()
        df['bb_lower'] = bollinger.bollinger_lband()
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['close']
        
        # ATR for volatility
        df['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
        
        return df
    
    def _calculate_trend_score(self, df):
        """Calculate trend score based on technical indicators"""
        score = 0.0
        latest = df.iloc[-1]
        
        # Price above EMAs
        if latest['close'] > latest['ema20']:
            score += 0.2
        if latest['ema20'] > latest['ema50']:
            score += 0.2
        
        # MACD
        if latest['macd'] > latest['macd_signal']:
            score += 0.3
        if latest['macd_diff'] > 0:
            score += 0.1
        
        # Bollinger Bands
        bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
        if 0.5 < bb_position < 0.8:  # In upper half but not overbought
            score += 0.2
        
        return score
    
    def _calculate_momentum_score(self, df):
        """Calculate momentum score based on technical indicators"""
        score = 0.0
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        # RSI
        if 40 < latest['rsi'] < 70:  # Not oversold or overbought
            score += 0.3
        if latest['rsi'] > prev['rsi']:  # Rising RSI
            score += 0.2
        
        # Volume
        avg_volume = df['volume'].rolling(20).mean().iloc[-1]
        if latest['volume'] > avg_volume * 1.5:  # Volume spike
            score += 0.3
        
        # Price momentum
        returns = df['close'].pct_change(5).iloc[-1] * 100  # 5-period returns
        if returns > 0:
            score += min(returns / 10, 0.2)  # Cap at 0.2
        
        return score
    
    def _save_analysis_details(self, coin_scores):
        """Save detailed analysis to file"""
        try:
            analysis_file = os.path.join(os.path.dirname(self.config['data_file']), 'coin_analysis.json')
            with open(analysis_file, 'w') as f:
                analysis_data = {
                    'timestamp': time.time(),
                    'update_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'coins': coin_scores[:30]  # Save top 30 coins for reference
                }
                json.dump(analysis_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving analysis details: {e}")

# Function to get selected coins
def get_selected_coins(client, config=None, force_update=False):
    """
    Get the selected coins for trading
    
    Args:
        client: Binance API client
        config: Configuration dictionary (optional)
        force_update: Force update regardless of the update interval
        
    Returns:
        list: List of coin symbols (e.g., ["BTCUSDT", "ETHUSDT"])
    """
    analyzer = CoinAnalyzer(client, config)
    return analyzer.get_top_coins(force_update)
