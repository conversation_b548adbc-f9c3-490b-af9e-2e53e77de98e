# Auto Mode and Web Service Guide

This document explains how to use the Auto Mode feature and configure the Web Service for the Trading Bot.

## Auto Mode

The Auto Mode allows the trading bot to automatically start and stop according to a predefined schedule. This is useful for running the bot during specific trading hours or days without manual intervention.

### Accessing Auto Mode

There are two ways to access the Auto Mode configuration:

1. From the Trading Bot Manager menu (option 7)
2. By directly running `auto_trading_mode.bat`

### Auto Mode Configuration

The Auto Mode configuration includes the following settings:

- **Enabled/Disabled**: Turn Auto Mode on or off
- **Investment Amount**: The amount in USDT to invest per trade
- **Start Time**: The time of day when the bot should start (24-hour format, e.g., "09:00")
- **End Time**: The time of day when the bot should stop (24-hour format, e.g., "17:00")
- **Trading Days**: The days of the week when the bot should run
- **Maximum Runtime**: Maximum number of hours the bot should run continuously
- **Auto Restart**: Whether the bot should automatically restart periodically
- **Restart Interval**: How often (in hours) the bot should restart if Auto Restart is enabled

### How Auto Mode Works

When Auto Mode is enabled:

1. The bot will automatically start at the configured start time on the configured trading days
2. It will run until either:
   - The end time is reached
   - The maximum runtime is reached
   - The bot is manually stopped
3. If Auto Restart is enabled, the bot will restart after the specified interval
4. The bot will automatically stop at the configured end time

The Auto Mode runner runs in a separate window and continuously monitors the schedule. It will start and stop the bot according to the configuration.

### Auto Mode Configuration File

The Auto Mode configuration is stored in `config/auto_mode/schedule.json`. This file is automatically created with default values when you first run the Auto Mode configuration.

Example configuration:

```json
{
  "enabled": true,
  "investment_amount": 30.0,
  "start_time": "09:00",
  "end_time": "17:00",
  "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  "max_runtime_hours": 8,
  "auto_restart": true,
  "restart_interval_hours": 24
}
```

## Web Service

The Trading Bot includes a web-based monitoring interface that allows you to track the bot's performance in real-time.

### Starting the Web Service

There are several ways to start the web service:

1. **Automatically with the bot**: The web service starts automatically when you start the trading bot using `start_trading.bat` or from the Trading Bot Manager.

2. **Standalone mode**: You can start only the web service without starting the trading bot by running `start_web_monitor.bat`.

3. **From the Trading Bot Manager**: Use option 3 "Open Web Monitor" to open the web interface in your browser. If the web service is not running, it will inform you.

### Web Service Configuration

The web service configuration is stored in the `web_monitor` section of the `config/config.json` file:

```json
"web_monitor": {
  "host": "0.0.0.0",
  "port": 5000,
  "refresh_interval": 30,
  "enable_notifications": false,
  "enabled": true
}
```

- **host**: The IP address to bind to (default: "0.0.0.0" which means all interfaces)
- **port**: The port to listen on (default: 5000)
- **refresh_interval**: How often the web interface should refresh data (in seconds)
- **enable_notifications**: Whether to enable browser notifications
- **enabled**: Whether the web service should be enabled

### Accessing the Web Interface

Once the web service is running, you can access the web interface by opening a web browser and navigating to:

- `http://localhost:5000` (if running on the same machine)
- `http://<your-ip-address>:5000` (if accessing from another device on the same network)

### Web Interface Features

The web interface provides:

1. **Performance Summary**: Total trades, win rate, profits, and losses
2. **Active Trades**: Currently open positions with real-time P/L
3. **Daily Performance**: Trades, profits, and losses per day
4. **Completed Trades**: History of all executed trades
5. **Selected Coins**: Currently monitored coins
6. **Recent Logs**: Latest activity logs

The dashboard automatically refreshes every 30 seconds to show the latest data.

### Stopping the Web Service

You can stop the web service in several ways:

1. **With the bot**: When you stop the trading bot using `stop_trading.bat`, the web service will also stop.
2. **Standalone**: Run `stop_web_monitor.bat` to stop only the web service.
3. **From the Trading Bot Manager**: Use option 4 "Stop All Services" to stop both the bot and the web service.

## Troubleshooting

### Web Service Issues

If the web interface is not accessible:

1. Check if the web service is running (look for a "Web Monitor" window)
2. Verify the port is not being used by another application
3. Check if your firewall is blocking the port
4. Try restarting the web service using `start_web_monitor.bat`

### Auto Mode Issues

If the Auto Mode is not working as expected:

1. Check if the Auto Mode runner is running (look for a "Trading Bot Auto Mode" window)
2. Verify the configuration in `config/auto_mode/schedule.json`
3. Check the system time and date to ensure they match your configured schedule
4. Try restarting the Auto Mode runner using `auto_trading_mode.bat`
