#!/usr/bin/env python3
"""
Final test to verify the auto_trading_mode.bat fix
"""

import os
import subprocess
import time

def test_configuration_loading():
    """Test if configuration loading works"""
    print("1. Testing configuration loading...")
    
    # Run read_config.py
    result = subprocess.run(['python', 'read_config.py'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✓ read_config.py works")
        
        # Check temp file
        if os.path.exists('config/auto_mode/temp.txt'):
            print("✓ temp.txt created")
            with open('config/auto_mode/temp.txt', 'r') as f:
                content = f.read()
            print(f"✓ temp.txt has {len(content.splitlines())} lines")
            return True
        else:
            print("❌ temp.txt not created")
            return False
    else:
        print(f"❌ read_config.py failed: {result.stderr}")
        return False

def test_toggle_scripts():
    """Test toggle scripts"""
    print("\n2. Testing toggle scripts...")
    
    # Test toggle_enabled.py
    result = subprocess.run(['python', 'toggle_enabled.py', 'true'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✓ toggle_enabled.py works")
    else:
        print(f"❌ toggle_enabled.py failed: {result.stderr}")
        return False
    
    # Test toggle_paper_trading.py
    result = subprocess.run(['python', 'toggle_paper_trading.py', 'true'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✓ toggle_paper_trading.py works")
        return True
    else:
        print(f"❌ toggle_paper_trading.py failed: {result.stderr}")
        return False

def test_main_with_amount():
    """Test main.py with --amount parameter"""
    print("\n3. Testing main.py with --amount parameter...")
    
    # Test if main.py accepts --amount parameter (just check help)
    result = subprocess.run(['python', 'main.py', '--help'], capture_output=True, text=True)
    if result.returncode == 0 and '--amount' in result.stdout:
        print("✓ main.py accepts --amount parameter")
        return True
    else:
        print("❌ main.py doesn't accept --amount parameter")
        return False

def test_web_monitor():
    """Test web monitor import"""
    print("\n4. Testing web monitor...")
    
    try:
        # Test if web monitor can be imported
        result = subprocess.run([
            'python', '-c', 
            'from web.monitor import create_templates, start_web_monitor; print("Web monitor import successful")'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ Web monitor imports successfully")
            return True
        else:
            print(f"❌ Web monitor import failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Web monitor import timed out")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("FINAL AUTO_TRADING_MODE.BAT FIX VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Toggle Scripts", test_toggle_scripts),
        ("Main.py with --amount", test_main_with_amount),
        ("Web Monitor", test_web_monitor),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name} Test...")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test FAILED with exception: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ AUTO_TRADING_MODE.BAT SHOULD NOW WORK!")
        print("\nTo test the fixed system:")
        print("1. Run: .\\auto_trading_mode.bat")
        print("2. Press 2 to toggle paper trading (should work now)")
        print("3. Press B to start auto mode (should work now)")
        print("4. Web monitor should start automatically")
        print("5. Trading bot should start with your settings")
        
        print("\n🚀 THE SYSTEM IS READY FOR MAXIMUM OUTPUT!")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    main()
