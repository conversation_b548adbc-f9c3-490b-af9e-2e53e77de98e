import json
import os
import sys

try:
    # Get the enabled status from command line argument
    new_enabled = sys.argv[1].lower() == 'true'
    
    # Ensure config directory exists
    os.makedirs('config/auto_mode', exist_ok=True)
    
    # Default config
    default_config = {
        'enabled': False,
        'investment_amount': 30.0,
        'start_time': '09:00',
        'end_time': '17:00',
        'days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        'max_runtime_hours': 8,
        'auto_restart': True,
        'restart_interval_hours': 24
    }
    
    # Load existing config if it exists
    config = default_config.copy()
    if os.path.exists('config/auto_mode/schedule.json'):
        try:
            with open('config/auto_mode/schedule.json', 'r') as f:
                content = f.read().strip()
                if content:
                    loaded_config = json.loads(content)
                    # Update config with loaded values
                    for key, value in loaded_config.items():
                        config[key] = value
        except Exception as e:
            print(f"Error loading config: {e}")
    
    # Update the enabled status
    config['enabled'] = new_enabled
    
    # Save the updated config
    with open('config/auto_mode/schedule.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    status = "enabled" if new_enabled else "disabled"
    print(f"Auto mode {status}.")
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
