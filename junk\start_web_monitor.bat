@echo off
setlocal enabledelayedexpansion
color 0A

echo ========================================================================
echo                  TRADING BOT WEB MONITOR STARTUP
echo ========================================================================
echo.

:: Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

:: Check for required packages
echo Checking for required packages...
set MISSING_PACKAGES=0

:: Check for flask
python -c "import flask" > nul 2>&1
if %errorlevel% neq 0 set MISSING_PACKAGES=1

:: Install missing packages if needed
if %MISSING_PACKAGES% equ 1 (
    echo Flask package is missing. Installing now...
    python -m pip install flask
    
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Flask.
        echo Please install it manually:
        echo python -m pip install flask
        pause
        exit /b 1
    ) else (
        echo Flask installed successfully.
    )
)

:: Check if web monitor is already running
tasklist /FI "WINDOWTITLE eq Web Monitor*" 2>nul | find "cmd.exe" >nul
if %errorlevel% equ 0 (
    echo Web Monitor is already running.
    echo.
    set /p RESTART="Do you want to restart it? (Y/N): "
    if /i "!RESTART!"=="Y" (
        echo Stopping current Web Monitor...
        taskkill /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
        taskkill /FI "IMAGENAME eq python.exe" /FI "WINDOWTITLE eq Web Monitor*" /F > nul 2>&1
        timeout /t 2 > nul
    ) else (
        echo Opening existing Web Monitor in browser...
        start http://localhost:5000
        echo.
        echo Press any key to exit...
        pause > nul
        exit /b 0
    )
)

:: Ask for web monitor configuration
echo.
echo Web Monitor Configuration:
echo.

:: Read current configuration
python -c "import json; config = {}; 
try:
    with open('config/config.json', 'r') as f:
        config = json.load(f)
except:
    pass
print(f'Current host: {config.get(\"web_monitor\", {}).get(\"host\", \"0.0.0.0\")}')
print(f'Current port: {config.get(\"web_monitor\", {}).get(\"port\", 5000)}')
" > config\temp.txt

for /f "tokens=*" %%a in (config\temp.txt) do echo %%a
del config\temp.txt

echo.
set /p CHANGE_CONFIG="Do you want to change the web monitor configuration? (Y/N): "

if /i "%CHANGE_CONFIG%"=="Y" (
    echo.
    set /p HOST="Enter host (default: 0.0.0.0): "
    set /p PORT="Enter port (default: 5000): "
    
    if "%HOST%"=="" set HOST=0.0.0.0
    if "%PORT%"=="" set PORT=5000
    
    :: Update configuration
    python -c "import json; 
config = {}
try:
    with open('config/config.json', 'r') as f:
        config = json.load(f)
except:
    pass
if 'web_monitor' not in config:
    config['web_monitor'] = {}
config['web_monitor']['host'] = '%HOST%'
config['web_monitor']['port'] = int('%PORT%')
config['web_monitor']['enabled'] = True
with open('config/config.json', 'w') as f:
    json.dump(config, f, indent=2)
print('Configuration updated successfully.')
"
) else (
    :: Use default configuration
    set HOST=0.0.0.0
    set PORT=5000
)

echo.
echo Starting Web Monitor on http://%HOST%:%PORT%...
echo.

:: Start the web monitor in a new window
start "Web Monitor" cmd /c "python -c \"from web.monitor import start_web_monitor; start_web_monitor('%HOST%', %PORT%)\""
timeout /t 3 > nul

:: Open the web interface in the default browser
echo Opening Web Interface in browser...
if "%HOST%"=="0.0.0.0" (
    start http://localhost:%PORT%
) else (
    start http://%HOST%:%PORT%
)

echo.
echo Web Monitor started successfully.
echo.
echo To stop the Web Monitor, close its window or run stop_web_monitor.bat
echo.
echo Press any key to exit...
pause > nul

endlocal
