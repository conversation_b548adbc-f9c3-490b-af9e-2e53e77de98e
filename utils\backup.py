"""
Backup Module

This module handles automatic backups of important data files:
- Trade history
- Performance data
- Configuration files
- Log files

It creates timestamped backups and manages retention of old backups.
"""

import os
import shutil
import logging
import datetime
import json
import glob

# Configure logging
logger = logging.getLogger(__name__)

class BackupManager:
    """Class for managing automatic backups"""
    
    def __init__(self, config=None):
        """
        Initialize the backup manager
        
        Args:
            config: Backup configuration dictionary
        """
        self.config = config or {}
        
        # Default configuration
        self.backup_dir = self.config.get('backup_dir', 'data/backups')
        self.max_backups = self.config.get('max_backups', 10)
        self.backup_interval = self.config.get('backup_interval', 86400)  # 24 hours
        self.last_backup_time = 0
        
        # Files to backup
        self.files_to_backup = self.config.get('files_to_backup', [
            'data/trade_history.json',
            'data/selected_coins.json',
            'data/performance_data.json',
            'config/config.json',
            'logs/trading_log.txt'
        ])
        
        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, force=False):
        """
        Create a backup of important files
        
        Args:
            force: Force backup regardless of the backup interval
            
        Returns:
            bool: True if backup was created successfully
        """
        current_time = datetime.datetime.now().timestamp()
        
        # Check if backup is needed
        if not force and (current_time - self.last_backup_time) < self.backup_interval:
            logger.debug("Skipping backup, not enough time has passed since last backup")
            return False
        
        try:
            # Create timestamp for backup folder
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_folder = os.path.join(self.backup_dir, f"backup_{timestamp}")
            os.makedirs(backup_folder, exist_ok=True)
            
            # Copy files to backup folder
            backed_up_files = []
            for file_path in self.files_to_backup:
                if os.path.exists(file_path):
                    # Create directory structure in backup folder
                    dest_dir = os.path.join(backup_folder, os.path.dirname(file_path))
                    os.makedirs(dest_dir, exist_ok=True)
                    
                    # Copy file
                    dest_path = os.path.join(backup_folder, file_path)
                    shutil.copy2(file_path, dest_path)
                    backed_up_files.append(file_path)
            
            # Create backup info file
            info = {
                'timestamp': timestamp,
                'datetime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'files': backed_up_files
            }
            
            with open(os.path.join(backup_folder, 'backup_info.json'), 'w') as f:
                json.dump(info, f, indent=2)
            
            logger.info(f"Created backup in {backup_folder} with {len(backed_up_files)} files")
            
            # Update last backup time
            self.last_backup_time = current_time
            
            # Clean up old backups
            self._cleanup_old_backups()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Clean up old backups, keeping only the most recent ones"""
        try:
            # Get all backup folders
            backup_folders = glob.glob(os.path.join(self.backup_dir, "backup_*"))
            backup_folders.sort(reverse=True)  # Sort by name (newest first)
            
            # Remove old backups
            if len(backup_folders) > self.max_backups:
                for folder in backup_folders[self.max_backups:]:
                    shutil.rmtree(folder)
                    logger.info(f"Removed old backup: {folder}")
                
        except Exception as e:
            logger.error(f"Failed to clean up old backups: {e}")
    
    def restore_backup(self, backup_folder=None):
        """
        Restore files from a backup
        
        Args:
            backup_folder: Path to the backup folder to restore from.
                           If None, the most recent backup is used.
            
        Returns:
            bool: True if restore was successful
        """
        try:
            # If no backup folder specified, use the most recent one
            if backup_folder is None:
                backup_folders = glob.glob(os.path.join(self.backup_dir, "backup_*"))
                backup_folders.sort(reverse=True)  # Sort by name (newest first)
                
                if not backup_folders:
                    logger.error("No backups found to restore")
                    return False
                
                backup_folder = backup_folders[0]
            
            # Check if backup info file exists
            info_file = os.path.join(backup_folder, 'backup_info.json')
            if not os.path.exists(info_file):
                logger.error(f"Backup info file not found in {backup_folder}")
                return False
            
            # Load backup info
            with open(info_file, 'r') as f:
                info = json.load(f)
            
            # Restore files
            restored_files = []
            for file_path in info['files']:
                backup_file = os.path.join(backup_folder, file_path)
                
                if os.path.exists(backup_file):
                    # Create directory structure if needed
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    
                    # Copy file
                    shutil.copy2(backup_file, file_path)
                    restored_files.append(file_path)
            
            logger.info(f"Restored {len(restored_files)} files from backup {backup_folder}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restore backup: {e}")
            return False
    
    def list_backups(self):
        """
        List all available backups
        
        Returns:
            list: List of dictionaries with backup information
        """
        try:
            backups = []
            backup_folders = glob.glob(os.path.join(self.backup_dir, "backup_*"))
            backup_folders.sort(reverse=True)  # Sort by name (newest first)
            
            for folder in backup_folders:
                info_file = os.path.join(folder, 'backup_info.json')
                
                if os.path.exists(info_file):
                    with open(info_file, 'r') as f:
                        info = json.load(f)
                    
                    backups.append({
                        'folder': folder,
                        'timestamp': info.get('timestamp'),
                        'datetime': info.get('datetime'),
                        'file_count': len(info.get('files', []))
                    })
                else:
                    # If info file doesn't exist, use folder name
                    folder_name = os.path.basename(folder)
                    timestamp = folder_name.replace('backup_', '')
                    
                    backups.append({
                        'folder': folder,
                        'timestamp': timestamp,
                        'datetime': 'Unknown',
                        'file_count': 'Unknown'
                    })
            
            return backups
            
        except Exception as e:
            logger.error(f"Failed to list backups: {e}")
            return []

# Function to get backup manager instance
def get_backup_manager(config=None):
    """
    Get a backup manager instance
    
    Args:
        config: Backup configuration dictionary
        
    Returns:
        BackupManager: A backup manager instance
    """
    return BackupManager(config)
