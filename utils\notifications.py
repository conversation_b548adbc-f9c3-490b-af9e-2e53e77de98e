"""
Notifications Module

This module handles sending notifications via email and Telegram.
It's used to alert the user about important events like:
- Trade entries and exits
- Significant profits or losses
- Error conditions
- Daily performance summaries
"""

import os
import logging
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class NotificationManager:
    """Class for managing and sending notifications"""
    
    def __init__(self, config=None):
        """
        Initialize the notification manager
        
        Args:
            config: Notification configuration dictionary
        """
        self.config = config or {}
        self.enabled = self.config.get('enabled', False)
        
        # Email settings
        self.email_enabled = False
        if self.enabled and self.config.get('email', {}).get('enabled', False):
            self.email_enabled = True
            self.email_config = self.config.get('email', {})
            
        # Telegram settings
        self.telegram_enabled = False
        if self.enabled and self.config.get('telegram', {}).get('enabled', False):
            self.telegram_enabled = True
            self.telegram_config = self.config.get('telegram', {})
    
    def send_notification(self, subject, message, level='info'):
        """
        Send a notification via all enabled channels
        
        Args:
            subject: Notification subject
            message: Notification message
            level: Importance level ('info', 'warning', 'error', 'success')
        
        Returns:
            bool: True if notification was sent successfully
        """
        if not self.enabled:
            return False
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"[{timestamp}] {subject}\n\n{message}"
        
        success = True
        
        # Send email notification
        if self.email_enabled:
            email_success = self._send_email(subject, formatted_message, level)
            success = success and email_success
        
        # Send Telegram notification
        if self.telegram_enabled:
            telegram_success = self._send_telegram(formatted_message, level)
            success = success and telegram_success
        
        return success
    
    def _send_email(self, subject, message, level):
        """
        Send an email notification
        
        Args:
            subject: Email subject
            message: Email message
            level: Importance level
            
        Returns:
            bool: True if email was sent successfully
        """
        try:
            # Add level indicator to subject
            if level == 'warning':
                subject = f"⚠️ {subject}"
            elif level == 'error':
                subject = f"🚨 {subject}"
            elif level == 'success':
                subject = f"✅ {subject}"
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_config.get('from_email')
            msg['To'] = self.email_config.get('to_email')
            msg['Subject'] = subject
            
            # Add message body
            msg.attach(MIMEText(message, 'plain'))
            
            # Connect to SMTP server
            server = smtplib.SMTP(
                self.email_config.get('smtp_server'),
                self.email_config.get('smtp_port')
            )
            server.starttls()
            server.login(
                self.email_config.get('username'),
                self.email_config.get('password')
            )
            
            # Send email
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email notification sent: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    def _send_telegram(self, message, level):
        """
        Send a Telegram notification
        
        Args:
            message: Message to send
            level: Importance level
            
        Returns:
            bool: True if message was sent successfully
        """
        try:
            # Add level indicator to message
            if level == 'warning':
                message = f"⚠️ WARNING\n{message}"
            elif level == 'error':
                message = f"🚨 ERROR\n{message}"
            elif level == 'success':
                message = f"✅ SUCCESS\n{message}"
            else:
                message = f"ℹ️ INFO\n{message}"
            
            # Telegram API URL
            bot_token = self.telegram_config.get('bot_token')
            chat_id = self.telegram_config.get('chat_id')
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            
            # Send message
            response = requests.post(url, data={
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            })
            
            if response.status_code == 200:
                logger.info("Telegram notification sent")
                return True
            else:
                logger.error(f"Failed to send Telegram notification: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
    
    def notify_trade_entry(self, symbol, price, quantity, investment):
        """Send notification about a new trade entry"""
        subject = f"New Trade: {symbol}"
        message = (
            f"Entered new trade:\n"
            f"Symbol: {symbol}\n"
            f"Price: {price}\n"
            f"Quantity: {quantity}\n"
            f"Investment: {investment} USDT"
        )
        return self.send_notification(subject, message, 'info')
    
    def notify_trade_exit(self, symbol, entry_price, exit_price, profit_loss, profit_loss_pct, duration):
        """Send notification about a trade exit"""
        level = 'success' if profit_loss > 0 else 'warning'
        subject = f"Trade Closed: {symbol}"
        
        profit_loss_text = f"+{profit_loss:.2f}" if profit_loss > 0 else f"{profit_loss:.2f}"
        profit_loss_pct_text = f"+{profit_loss_pct:.2f}%" if profit_loss_pct > 0 else f"{profit_loss_pct:.2f}%"
        
        message = (
            f"Closed trade:\n"
            f"Symbol: {symbol}\n"
            f"Entry Price: {entry_price}\n"
            f"Exit Price: {exit_price}\n"
            f"P/L: {profit_loss_text} USDT ({profit_loss_pct_text})\n"
            f"Duration: {duration:.1f} minutes"
        )
        return self.send_notification(subject, message, level)
    
    def notify_daily_summary(self, date, trades_count, profit_loss, win_rate):
        """Send notification with daily performance summary"""
        level = 'success' if profit_loss > 0 else 'warning'
        subject = f"Daily Summary: {date}"
        
        profit_loss_text = f"+{profit_loss:.2f}" if profit_loss > 0 else f"{profit_loss:.2f}"
        
        message = (
            f"Daily trading summary:\n"
            f"Date: {date}\n"
            f"Total Trades: {trades_count}\n"
            f"P/L: {profit_loss_text} USDT\n"
            f"Win Rate: {win_rate:.1f}%"
        )
        return self.send_notification(subject, message, level)
    
    def notify_error(self, error_type, details):
        """Send notification about an error"""
        subject = f"Error: {error_type}"
        message = f"Error details: {details}"
        return self.send_notification(subject, message, 'error')

# Function to get notification manager instance
def get_notification_manager(config=None):
    """
    Get a notification manager instance
    
    Args:
        config: Notification configuration dictionary
        
    Returns:
        NotificationManager: A notification manager instance
    """
    return NotificationManager(config)
