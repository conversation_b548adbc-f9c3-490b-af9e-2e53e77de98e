# Crypto Trading Bot User Guide

This guide provides step-by-step instructions for setting up and using the high-performance crypto trading bot.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Starting the Bot](#starting-the-bot)
5. [Using the Web Monitor](#using-the-web-monitor)
6. [Understanding the Trading Strategy](#understanding-the-trading-strategy)
7. [Monitoring Performance](#monitoring-performance)
8. [Adjusting Settings](#adjusting-settings)
9. [Troubleshooting](#troubleshooting)
10. [FAQ](#faq)

## Prerequisites

Before you begin, ensure you have:

1. **Python 3.7 or higher** installed on your system
2. **Binance account** with API access enabled
3. **Sufficient USDT** in your Binance account (minimum 30 USDT recommended)
4. **Basic understanding** of cryptocurrency trading

## Installation

### Step 1: Download the Bot

Clone or download the repository to your local machine:

```bash
git clone https://github.com/yourusername/crypto-trading-bot.git
cd crypto-trading-bot
```

### Step 2: Install Required Packages

Install all required dependencies using pip:

```bash
pip install -r requirements.txt
```

This will install:
- python-binance (for Binance API access)
- pandas (for data manipulation)
- ta (for technical analysis)
- python-dotenv (for environment variables)
- flask (for web monitoring interface)

### Step 3: Set Up API Keys

1. Log in to your Binance account
2. Navigate to API Management
3. Create a new API key (enable trading permissions, but NOT withdrawal permissions)
4. Create a `.env` file in the project directory with your API credentials:

```
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
```

## Configuration

The bot comes pre-configured for optimal performance with a 30 USDT investment over a 7-day period. However, you can customize various settings in the `fast_trading_bot.py` file:

### Trading Pairs

The bot is configured to trade these high-volatility pairs:

```python
PAIRS = [
    "INJUSDT",   # Injective - DeFi with high volatility
    "FETUSDT",   # Fetch.ai - AI token with growth potential
    "AGIXUSDT",  # SingularityNET - AI token with volatility
    "APTUSDT",   # Aptos - Layer 1 with strong momentum
    "SUIUSDT",   # Sui - New L1 blockchain with volatility
    "PEPEUSDT",  # Pepe - Meme coin with high volatility
    "FLOKIUSDT"  # Floki - Meme coin with high volatility
]
```

You can modify this list to include any trading pairs available on Binance.

### Risk Parameters

```python
STOP_LOSS_PERCENT = 5.0        # Maximum loss percentage before exit
TAKE_PROFIT_PERCENT = 15.0     # Target profit percentage
TRAILING_STOP_PERCENT = 3.0    # How far price can fall from highest point
MAX_TRADES_PER_DAY = 10        # Maximum trades per day
INVESTMENT_PERIOD_DAYS = 7     # Duration for tracking performance
```

## Starting the Bot

### Step 1: Run the Trading Bot

Open a terminal in the project directory and run:

```bash
python fast_trading_bot.py
```

### Step 2: Enter Trade Amount

When prompted, enter your desired trade amount in USDT (default is 30 USDT):

```
Enter trade amount in USDT (default: 30.0):
```

Press Enter to use the default amount, or type a different amount and press Enter.

### Step 3: Monitor Initial Output

The bot will start by displaying information about:
- Trading period (start and end dates)
- Pairs being monitored
- Target profit percentage

It will then begin scanning for trading opportunities based on the configured strategy.

## Using the Web Monitor

The bot includes a web-based monitoring interface that provides real-time insights into your trading performance.

### Step 1: Start the Web Monitor

Open a new terminal window (keep the bot running in the first terminal) and run:

```bash
python trade_monitor.py
```

### Step 2: Access the Web Interface

Open your web browser and navigate to:

```
http://localhost:5000
```

### Step 3: Understand the Dashboard

The web interface is divided into four main sections:

1. **Performance Summary** (top left):
   - Total trades executed
   - Win rate percentage
   - Profitable and losing trade counts
   - Total profit/loss
   - Total profit and loss separately
   - Daily ROI (Return on Investment)

2. **Active Trades** (top right):
   - Currently open positions
   - Current price and profit/loss
   - Time of entry

3. **Daily Performance** (middle):
   - Number of trades per day
   - Daily profit amount
   - Daily loss amount
   - Daily net profit/loss

4. **Completed Trades** (bottom):
   - Complete history of all trades
   - Entry and exit prices
   - Profit/loss amount and percentage
   - Duration of each trade

The dashboard automatically refreshes every 30 seconds to show the latest data.

## Understanding the Trading Strategy

The bot uses a sophisticated multi-indicator approach to identify high-probability trading opportunities:

### Signal Generation

The bot calculates a signal strength score (0-12 points) based on multiple factors:

1. **Trend Indicators** (3 points max)
   - Price above EMA20
   - EMA20 above EMA50
   - Current price higher than previous price

2. **Momentum Indicators** (4 points max)
   - RSI between 30-40 (oversold but recovering)
   - Rising RSI
   - Stochastic crossover from oversold

3. **MACD Indicators** (3 points max)
   - Fresh MACD crossover
   - Increasing MACD histogram

4. **Volatility Indicators** (2 points max)
   - Price below lower Bollinger Band
   - Expanding Bollinger Band width

5. **Volume Indicators** (1 point max)
   - Volume spike (1.5x above average)

A trade is only executed when:
- The signal strength score is 7 or higher (out of 12)
- Signals are confirmed on both 5-minute and 15-minute timeframes

### Exit Criteria

The bot will exit a trade when one of these conditions is met:
- Take profit target reached (15% profit)
- Trailing stop triggered (price drops 3% from highest point)
- Maximum trade duration reached (8 hours)

## Monitoring Performance

### Trading Log

The bot creates a detailed log file (`trading_log.txt`) that records all activities:
- Trade entries and exits
- Profit/loss for each trade
- Error messages
- Performance reports

You can view this file at any time to see detailed information about the bot's operation.

### Performance Reports

The bot automatically generates performance reports:
- At the end of each day
- At the end of the investment period
- When the bot is stopped (Ctrl+C)

These reports include:
- Total trades executed
- Win rate
- Total profit/loss
- Best performing pairs
- Daily returns

## Adjusting Settings

After running the bot for a few days, you may want to adjust settings based on performance:

### Increasing Investment

If the bot is performing well, you can increase your investment amount when starting the bot:
```
Enter trade amount in USDT (default: 30.0): 50
```

### Modifying Trading Pairs

You can edit the `PAIRS` list in `fast_trading_bot.py` to focus on the best-performing pairs or add new ones.

### Adjusting Risk Parameters

If needed, you can modify:
- `TAKE_PROFIT_PERCENT`: Increase for higher potential returns (but potentially fewer trades)
- `STOP_LOSS_PERCENT`: Decrease for tighter risk management
- `TRAILING_STOP_PERCENT`: Adjust to lock in more or less profit

## Troubleshooting

### Common Issues

1. **API Connection Problems**
   - Check your API keys are correct in the `.env` file
   - Ensure your IP is whitelisted in Binance
   - Verify your internet connection

2. **Order Execution Failures**
   - Check you have sufficient balance
   - Verify the trading pair is active
   - Check for any Binance maintenance windows

3. **Web Monitor Not Working**
   - Ensure Flask is installed (`pip install flask`)
   - Check if port 5000 is available on your system
   - Restart the web monitor

4. **Unicode/Emoji Errors in Windows Command Prompt**
   - If you see errors related to Unicode characters or emojis in the logs, this is a known issue with Windows command prompt
   - The bot has been updated to use plain text log messages instead of emojis
   - If you still encounter these errors, consider running the bot in a terminal that supports Unicode (like Windows Terminal or PowerShell)

### Error Messages

If you encounter errors, check the `trading_log.txt` file for detailed error messages.

## FAQ

**Q: How much money can I expect to make?**

A: The bot targets 15-30% returns, but cryptocurrency trading involves significant risk. Past performance is not indicative of future results.

**Q: Can I run the bot 24/7?**

A: Yes, the bot is designed for continuous operation. For best results, run it on a stable internet connection.

**Q: Will the bot work with other exchanges?**

A: Currently, the bot only supports Binance. Support for other exchanges may be added in future versions.

**Q: What happens if my internet connection drops?**

A: The bot will attempt to reconnect. Any active trades will continue to be monitored once the connection is restored.

**Q: Is it safe to use my Binance API keys?**

A: Yes, if you follow security best practices:
- Never enable withdrawal permissions for API keys used with this bot
- Keep your API secret secure and never share it
- Run the bot on a secure computer

**Q: How can I stop the bot?**

A: Press Ctrl+C in the terminal where the bot is running. The bot will generate a final performance report before stopping.
