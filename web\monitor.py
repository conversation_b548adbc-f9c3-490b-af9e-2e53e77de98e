"""
Web Monitor Module

This module provides a web-based interface for monitoring the trading bot.
It displays real-time information about:
- Active trades
- Trade history
- Performance metrics
- Daily statistics
"""

import os
import json
import time
import logging
import threading
import datetime
from flask import Flask, render_template, jsonify, request, send_from_directory

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Global variables to store trading data
trade_data = {
    'active_trades': [],
    'completed_trades': [],
    'trading_mode': 'UNKNOWN',
    'paper_balance': 0.0,
    'performance': {
        'total_trades': 0,
        'profitable_trades': 0,
        'losing_trades': 0,
        'total_profit_loss': 0.0,
        'total_profit': 0.0,
        'total_loss': 0.0,
        'win_rate': 0.0,
        'daily_roi': 0.0
    },
    'pair_performance': {},
    'daily_profits': {},
    'daily_losses': {},
    'daily_trades': {},
    'daily_net': {},
    'selected_coins': [],
    'last_update': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
}

# Paths to data files
LOG_FILE = 'logs/trading_log.txt'
TRADE_HISTORY_FILE = 'data/trade_history.json'
PERFORMANCE_DATA_FILE = 'data/performance_data.json'
SELECTED_COINS_FILE = 'data/selected_coins.json'
COIN_ANALYSIS_FILE = 'data/coin_analysis.json'

# Function to update trade data
def update_trade_data():
    """Update trade data from files"""
    global trade_data
    
    try:
        # Check trading mode from config
        config_file = 'config/config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
                paper_trading = config.get('trading', {}).get('paper_trading', False)
                paper_mode = config.get('advanced', {}).get('paper_trading_mode', False)
                
                if paper_trading or paper_mode:
                    trade_data['trading_mode'] = 'PAPER TRADING'
                    trade_data['paper_balance'] = config.get('trading', {}).get('paper_balance', 1000.0)
                else:
                    trade_data['trading_mode'] = 'LIVE TRADING'
                    trade_data['paper_balance'] = 0.0
        
        # Check for paper trading portfolio file
        paper_portfolio_file = 'data/paper_portfolio.json'
        if os.path.exists(paper_portfolio_file) and trade_data['trading_mode'] == 'PAPER TRADING':
            with open(paper_portfolio_file, 'r') as f:
                paper_data = json.load(f)
                trade_data['paper_balance'] = paper_data.get('balance', trade_data['paper_balance'])
        
        # Update from trade history file
        if os.path.exists(TRADE_HISTORY_FILE):
            with open(TRADE_HISTORY_FILE, 'r') as f:
                trade_history = json.load(f)
                
                # Process completed trades
                completed_trades = []
                for trade in trade_history:
                    # Convert string dates to datetime objects for sorting
                    if 'date' in trade and isinstance(trade['date'], str):
                        trade['date_obj'] = datetime.datetime.strptime(trade['date'], '%Y-%m-%d %H:%M:%S')
                    else:
                        trade['date_obj'] = datetime.datetime.now()
                    
                    completed_trades.append(trade)
                
                # Sort by date (newest first)
                completed_trades.sort(key=lambda x: x['date_obj'], reverse=True)
                
                # Remove temporary date_obj field
                for trade in completed_trades:
                    if 'date_obj' in trade:
                        del trade['date_obj']
                
                trade_data['completed_trades'] = completed_trades
                
                # Calculate statistics
                total_trades = len(completed_trades)
                profitable_trades = sum(1 for t in completed_trades if t.get('profit_loss', 0) > 0)
                losing_trades = total_trades - profitable_trades
                
                total_profit_loss = sum(t.get('profit_loss', 0) for t in completed_trades)
                total_profit = sum(t.get('profit_loss', 0) for t in completed_trades if t.get('profit_loss', 0) > 0)
                total_loss = sum(t.get('profit_loss', 0) for t in completed_trades if t.get('profit_loss', 0) <= 0)
                
                win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
                
                # Update performance data
                trade_data['performance']['total_trades'] = total_trades
                trade_data['performance']['profitable_trades'] = profitable_trades
                trade_data['performance']['losing_trades'] = losing_trades
                trade_data['performance']['total_profit_loss'] = total_profit_loss
                trade_data['performance']['total_profit'] = total_profit
                trade_data['performance']['total_loss'] = total_loss
                trade_data['performance']['win_rate'] = win_rate
                
                # Calculate daily statistics
                daily_profits = {}
                daily_losses = {}
                daily_trades = {}
                daily_net = {}
                
                for trade in completed_trades:
                    # Get the date part only
                    if isinstance(trade.get('date'), str):
                        trade_date = datetime.datetime.strptime(trade.get('date'), '%Y-%m-%d %H:%M:%S').date().strftime('%Y-%m-%d')
                    else:
                        trade_date = trade.get('date').date().strftime('%Y-%m-%d')
                    
                    # Count trades per day
                    if trade_date not in daily_trades:
                        daily_trades[trade_date] = 0
                        daily_profits[trade_date] = 0.0
                        daily_losses[trade_date] = 0.0
                        daily_net[trade_date] = 0.0
                    
                    daily_trades[trade_date] += 1
                    
                    # Track profit/loss
                    profit_loss = trade.get('profit_loss', 0)
                    daily_net[trade_date] += profit_loss
                    
                    if profit_loss > 0:
                        daily_profits[trade_date] += profit_loss
                    else:
                        daily_losses[trade_date] += profit_loss
                
                # Update daily statistics
                trade_data['daily_profits'] = daily_profits
                trade_data['daily_losses'] = daily_losses
                trade_data['daily_trades'] = daily_trades
                trade_data['daily_net'] = daily_net
                
                # Calculate daily ROI based on average daily net profit
                if daily_net:
                    avg_daily_net = sum(daily_net.values()) / len(daily_net)
                    # Assuming investment amount is 30 USDT
                    trade_data['performance']['daily_roi'] = (avg_daily_net / 30.0) * 100
        
        # Update from performance data file
        if os.path.exists(PERFORMANCE_DATA_FILE):
            with open(PERFORMANCE_DATA_FILE, 'r') as f:
                performance_data = json.load(f)
                
                # Update pair performance
                if 'pair_performance' in performance_data:
                    trade_data['pair_performance'] = performance_data['pair_performance']
        
        # Update selected coins
        if os.path.exists(SELECTED_COINS_FILE):
            with open(SELECTED_COINS_FILE, 'r') as f:
                coins_data = json.load(f)
                
                if 'coins' in coins_data:
                    trade_data['selected_coins'] = coins_data['coins']
        
        # Parse log file for active trades
        active_trades = []
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, 'r') as f:
                lines = f.readlines()
                
                # Find active trades
                for i in range(len(lines) - 1, 0, -1):
                    line = lines[i]
                    
                    # Check for monitoring messages (active trades)
                    if "[MONITOR]" in line and "Current:" in line:
                        parts = line.split('|')
                        if len(parts) >= 3:
                            # Extract symbol from the line
                            symbol_part = parts[0].split("[MONITOR]")[1].strip()
                            current_part = parts[1].strip()
                            pnl_part = parts[2].strip()
                            
                            # Check if this symbol is already in active_trades
                            if not any(t['symbol'] == symbol_part for t in active_trades):
                                active_trades.append({
                                    'symbol': symbol_part,
                                    'current_price': current_part.replace('Current:', '').strip(),
                                    'pnl': pnl_part.replace('P/L:', '').strip(),
                                    'time': line.split(' - ')[0].strip()
                                })
        
        trade_data['active_trades'] = active_trades
        trade_data['last_update'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
    except Exception as e:
        logger.error(f"Error updating trade data: {e}")

# Background thread to update trade data periodically
def background_updater():
    """Update trade data in the background"""
    while True:
        try:
            update_trade_data()
            time.sleep(10)  # Update every 10 seconds
        except Exception as e:
            logger.error(f"Error in background updater: {e}")
            time.sleep(30)  # Wait longer if there's an error

# Routes
@app.route('/')
def index():
    """Render the main dashboard"""
    return render_template('monitor.html')

@app.route('/api/data')
def get_data():
    """API endpoint to get trading data"""
    return jsonify(trade_data)

@app.route('/api/coins')
def get_coins():
    """API endpoint to get coin analysis data"""
    try:
        if os.path.exists(COIN_ANALYSIS_FILE):
            with open(COIN_ANALYSIS_FILE, 'r') as f:
                return jsonify(json.load(f))
        return jsonify({'error': 'Coin analysis file not found'})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/logs')
def get_logs():
    """API endpoint to get recent logs"""
    try:
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, 'r') as f:
                lines = f.readlines()
                return jsonify({'logs': lines[-100:]})  # Return last 100 lines
        return jsonify({'error': 'Log file not found'})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/static/<path:path>')
def send_static(path):
    """Serve static files"""
    return send_from_directory('web/static', path)

# Create HTML template directory and file
def create_templates():
    """Create the HTML templates"""
    os.makedirs('web/templates', exist_ok=True)
    os.makedirs('web/static', exist_ok=True)
    
    # Create monitor.html template
    with open('web/templates/monitor.html', 'w') as f:
        f.write("""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .card { margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .card-header { font-weight: bold; background-color: #343a40; color: white; }
        .profit { color: green; }
        .loss { color: red; }
        .refresh-time { font-size: 0.8rem; color: #6c757d; text-align: right; }
        .trade-row:hover { background-color: #f1f1f1; }
        .nav-tabs .nav-link { color: #495057; }
        .nav-tabs .nav-link.active { font-weight: bold; }
    </style>
    <meta http-equiv="refresh" content="30">
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4 text-center">Trading Bot Monitor</h1>
        <div class="refresh-time mb-3">Last updated: <span id="last-update"></span></div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Performance Summary</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <p><strong>Total Trades:</strong> <span id="total-trades">0</span></p>
                                <p><strong>Win Rate:</strong> <span id="win-rate">0%</span></p>
                                <p><strong>Profitable Trades:</strong> <span id="profitable-trades">0</span></p>
                                <p><strong>Losing Trades:</strong> <span id="losing-trades">0</span></p>
                            </div>
                            <div class="col-6">
                                <p><strong>Total P/L:</strong> <span id="total-pnl">0.00 USDT</span></p>
                                <p><strong>Total Profit:</strong> <span id="total-profit" class="profit">0.00 USDT</span></p>
                                <p><strong>Total Loss:</strong> <span id="total-loss" class="loss">0.00 USDT</span></p>
                                <p><strong>Daily ROI:</strong> <span id="daily-roi">0.00%</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Active Trades</div>
                    <div class="card-body">
                        <div id="active-trades-container">
                            <p class="text-center text-muted">No active trades</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">Daily Performance</div>
                    <div class="card-body">
                        <div id="daily-stats-container">
                            <p class="text-center text-muted">No daily statistics available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="trades-tab" data-bs-toggle="tab" data-bs-target="#trades" type="button" role="tab" aria-controls="trades" aria-selected="true">Completed Trades</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="coins-tab" data-bs-toggle="tab" data-bs-target="#coins" type="button" role="tab" aria-controls="coins" aria-selected="false">Selected Coins</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">Recent Logs</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="trades" role="tabpanel" aria-labelledby="trades-tab">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Buy Price</th>
                                                <th>Sell Price</th>
                                                <th>Quantity</th>
                                                <th>P/L</th>
                                                <th>P/L %</th>
                                                <th>Date</th>
                                                <th>Duration</th>
                                            </tr>
                                        </thead>
                                        <tbody id="completed-trades">
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">No completed trades</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="coins" role="tabpanel" aria-labelledby="coins-tab">
                                <div id="selected-coins-container">
                                    <p class="text-center text-muted">No coin data available</p>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                                <div id="logs-container" style="max-height: 400px; overflow-y: auto;">
                                    <p class="text-center text-muted">No logs available</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to fetch and update data
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Update performance metrics
                    document.getElementById('total-trades').textContent = data.performance.total_trades;
                    document.getElementById('profitable-trades').textContent = data.performance.profitable_trades;
                    document.getElementById('losing-trades').textContent = data.performance.losing_trades;
                    document.getElementById('win-rate').textContent = data.performance.win_rate.toFixed(1) + '%';
                    
                    // Update total P/L
                    const pnlElement = document.getElementById('total-pnl');
                    pnlElement.textContent = data.performance.total_profit_loss.toFixed(2) + ' USDT';
                    if (data.performance.total_profit_loss > 0) {
                        pnlElement.className = 'profit';
                    } else if (data.performance.total_profit_loss < 0) {
                        pnlElement.className = 'loss';
                    }
                    
                    // Update total profit and loss
                    document.getElementById('total-profit').textContent = data.performance.total_profit.toFixed(2) + ' USDT';
                    document.getElementById('total-loss').textContent = data.performance.total_loss.toFixed(2) + ' USDT';
                    
                    // Update daily ROI
                    const roiElement = document.getElementById('daily-roi');
                    roiElement.textContent = data.performance.daily_roi.toFixed(2) + '%';
                    if (data.performance.daily_roi > 0) {
                        roiElement.className = 'profit';
                    } else if (data.performance.daily_roi < 0) {
                        roiElement.className = 'loss';
                    }
                    
                    // Update active trades
                    const activeTradesContainer = document.getElementById('active-trades-container');
                    if (data.active_trades.length > 0) {
                        let activeTradesHtml = '<div class="table-responsive"><table class="table table-sm">';
                        activeTradesHtml += '<thead><tr><th>Symbol</th><th>Current Price</th><th>P/L</th><th>Time</th></tr></thead><tbody>';
                        
                        data.active_trades.forEach(trade => {
                            const isProfitable = trade.pnl.includes('+') || !trade.pnl.includes('-');
                            activeTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${trade.current_price}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.pnl}</td>
                                <td>${trade.time}</td>
                            </tr>`;
                        });
                        
                        activeTradesHtml += '</tbody></table></div>';
                        activeTradesContainer.innerHTML = activeTradesHtml;
                    } else {
                        activeTradesContainer.innerHTML = '<p class="text-center text-muted">No active trades</p>';
                    }
                    
                    // Update daily statistics
                    const dailyStatsContainer = document.getElementById('daily-stats-container');
                    if (Object.keys(data.daily_trades).length > 0) {
                        let dailyStatsHtml = '<div class="table-responsive"><table class="table table-striped">';
                        dailyStatsHtml += '<thead><tr><th>Date</th><th>Trades</th><th>Profit</th><th>Loss</th><th>Net P/L</th></tr></thead><tbody>';
                        
                        // Get dates and sort them (newest first)
                        const dates = Object.keys(data.daily_trades).sort().reverse();
                        
                        dates.forEach(date => {
                            const trades = data.daily_trades[date];
                            const profit = data.daily_profits[date] || 0;
                            const loss = data.daily_losses[date] || 0;
                            const net = data.daily_net[date] || 0;
                            
                            const isProfitable = net > 0;
                            
                            dailyStatsHtml += `<tr>
                                <td>${date}</td>
                                <td>${trades}</td>
                                <td class="profit">${profit.toFixed(2)} USDT</td>
                                <td class="loss">${loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${net.toFixed(2)} USDT</td>
                            </tr>`;
                        });
                        
                        dailyStatsHtml += '</tbody></table></div>';
                        dailyStatsContainer.innerHTML = dailyStatsHtml;
                    } else {
                        dailyStatsContainer.innerHTML = '<p class="text-center text-muted">No daily statistics available</p>';
                    }
                    
                    // Update completed trades
                    const completedTradesTable = document.getElementById('completed-trades');
                    if (data.completed_trades.length > 0) {
                        let completedTradesHtml = '';
                        
                        data.completed_trades.forEach(trade => {
                            const isProfitable = trade.profit_loss > 0;
                            completedTradesHtml += `<tr class="trade-row">
                                <td>${trade.symbol}</td>
                                <td>${parseFloat(trade.buy_price).toFixed(8)}</td>
                                <td>${parseFloat(trade.sell_price).toFixed(8)}</td>
                                <td>${trade.quantity}</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss.toFixed(2)} USDT</td>
                                <td class="${isProfitable ? 'profit' : 'loss'}">${trade.profit_loss_pct.toFixed(2)}%</td>
                                <td>${trade.date}</td>
                                <td>${trade.duration_minutes.toFixed(1)} min</td>
                            </tr>`;
                        });
                        
                        completedTradesTable.innerHTML = completedTradesHtml;
                    } else {
                        completedTradesTable.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No completed trades</td></tr>';
                    }
                    
                    // Update selected coins
                    const selectedCoinsContainer = document.getElementById('selected-coins-container');
                    if (data.selected_coins && data.selected_coins.length > 0) {
                        let coinsHtml = '<div class="alert alert-info">Currently monitoring the following coins:</div>';
                        coinsHtml += '<div class="row">';
                        
                        data.selected_coins.forEach(coin => {
                            coinsHtml += `<div class="col-md-3 mb-2"><div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">${coin}</h5>
                                </div>
                            </div></div>`;
                        });
                        
                        coinsHtml += '</div>';
                        selectedCoinsContainer.innerHTML = coinsHtml;
                    } else {
                        selectedCoinsContainer.innerHTML = '<p class="text-center text-muted">No coins selected</p>';
                    }
                    
                    // Update last update time
                    document.getElementById('last-update').textContent = data.last_update;
                })
                .catch(error => console.error('Error fetching data:', error));
                
            // Fetch logs
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    if (data.logs) {
                        const logsContainer = document.getElementById('logs-container');
                        let logsHtml = '<pre style="max-height: 400px; overflow-y: auto;">';
                        
                        data.logs.forEach(line => {
                            logsHtml += line;
                        });
                        
                        logsHtml += '</pre>';
                        logsContainer.innerHTML = logsHtml;
                    }
                })
                .catch(error => console.error('Error fetching logs:', error));
        }
        
        // Initial update
        updateData();
        
        // Update every 10 seconds
        setInterval(updateData, 10000);
    </script>
</body>
</html>
""")
    
    logger.info("Created web templates")

def start_web_monitor(host='0.0.0.0', port=5000):
    """
    Start the web monitor
    
    Args:
        host: Host to bind to
        port: Port to listen on
    """
    try:
        # Create templates
        create_templates()
        
        # Start background updater thread
        updater_thread = threading.Thread(target=background_updater, daemon=True)
        updater_thread.start()
        
        # Start Flask app
        app.run(host=host, port=port, debug=False)
        
    except Exception as e:
        logger.error(f"Error starting web monitor: {e}")

if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    # Start web monitor
    start_web_monitor()
